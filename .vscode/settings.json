{
    "[dart]": {
        "editor.tabSize": 2,
        "editor.insertSpaces": true,
        "editor.detectIndentation": false,
        "editor.suggest.insertMode": "replace",
        "editor.formatOnSave": true,
        "editor.formatOnSaveMode": "file",
        "editor.rulers": [180]
    },
    "dart.lineLength": 180,
    "dart.flutterSdkPath": ".fvm/flutter_sdk",
    "dart.devToolsLocation": {
        "default": "external"
    },
    "launch": {
        "version": "0.2.0",
        "configurations": [
            {
                "name": "CM Capital (hml)",
                "program": "lib/main.dart",
                "request": "launch",
                "type": "dart",
                "args": ["--flavor", "hml"]
            },
            {
                "name": "CM Capital (prod)",
                "program": "lib/main.dart",
                "request": "launch",
                "type": "dart",
                "args": ["--flavor", "prod"]
            },
            {
              "name": "Run pre-buit apk (hml)",
              "program": "lib/main.dart",
              "request": "launch",
              "type": "dart",
              "args": ["--use-application-binary=build/app/outputs/flutter-apk/app-hml-debug.apk"]
            },
            {
              "name": "Run pre-buit apk (prod)",
              "program": "lib/main.dart",
              "request": "launch",
              "type": "dart",
              "args": ["--use-application-binary=build/app/outputs/flutter-apk/app-prod-debug.apk"]
            },
        ]
    }
}