import 'package:json_annotation/json_annotation.dart';

part 'usuario_alterar_assinatura_validar_request.g.dart';

@JsonSerializable()
class UsuarioAlterarAssinaturaValidarRequest {
  @Json<PERSON>ey(name: 'assinatura_nova')
  final String newSignature;

  UsuarioAlterarAssinaturaValidarRequest({required this.newSignature});

  factory UsuarioAlterarAssinaturaValidarRequest.fromJson(Map<String, dynamic> json) => _$UsuarioAlterarAssinaturaValidarRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UsuarioAlterarAssinaturaValidarRequestToJson(this);
}
