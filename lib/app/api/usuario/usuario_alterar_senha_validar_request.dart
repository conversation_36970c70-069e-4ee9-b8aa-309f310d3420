import 'package:json_annotation/json_annotation.dart';

part 'usuario_alterar_senha_validar_request.g.dart';

@JsonSerializable()
class UsuarioAlterarSenhaValidarRequest {
  @JsonKey(name: 'senha_nova')
  final String newPassword;

  UsuarioAlterarSenhaValidarRequest({required this.newPassword});

  factory UsuarioAlterarSenhaValidarRequest.fromJson(Map<String, dynamic> json) => _$UsuarioAlterarSenhaValidarRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UsuarioAlterarSenhaValidarRequestToJson(this);
}
