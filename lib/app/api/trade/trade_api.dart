import 'package:dio/dio.dart' as dio;
import 'package:get/get.dart';

import '../../models/trade/trade_history.dart';
import '../../models/trade/trade_month_performance.dart';
import '../../models/trade/trade_required_balance_calculation.dart';
import '../../utils/secret.dart';
import '../cm_request.dart';

class TradeApi {
  final _request = Get.find<CMRequest>();

  Future<dio.Response> getTradeById({required String path}) {
    return _request.get(path);
  }

  Future<dio.Response> getTrades({required String path}) {
    return _request.get(path);
  }

  Future<List<TradeMonthPerformance>> getTradeLastYearHistory({required String path}) async {
    final response = await _request.get(path, encryptionOptions: const EncryptionOptions.noEncryption());
    return (response.data['mesesatividade'] as List).map((history) => TradeMonthPerformance.fromJson(history)).toList();
  }

  Future<dio.Response> getTradeMonthHistory({required String path, required DateTime monthDate}) {
    return _request.get(
      path,
      queryParameters: {
        'mes': monthDate.month,
        'ano': monthDate.year,
      },
      encryptionOptions: const EncryptionOptions.noEncryption(),
    );
  }

  Future<void> hireTrade({
    required String path,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) async {
    final data = {
      'valorPorRecomendacao': amount,
      'assinatura': getSecretPreEncryptionString(signature),
      'aceiteTermo': didAcceptTerms,
      'desenquadrado': isProfileSuitabilityUnfit,
    };
    if (contractCount != null) data['quantidadeDeContrato'] = contractCount;
    await _request.post(path, data, encryptionOptions: const EncryptionOptions.values({'assinatura': true}));
  }

  Future<void> editTrade({
    required String path,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) async {
    final data = {
      'valorPorRecomendacao': amount,
      'assinatura': getSecretPreEncryptionString(signature),
      'aceiteTermo': didAcceptTerms,
      'desenquadrado': isProfileSuitabilityUnfit,
    };
    if (contractCount != null) data['quantidadeDeContrato'] = contractCount;
    await _request.put(path, data, encryptionOptions: const EncryptionOptions.values({'assinatura': true}));
  }

  Future<void> deactivateTrade({
    required String path,
    required List<List<int>> signature,
  }) async {
    await _request.put(
      path,
      {
        'assinatura': getSecretPreEncryptionString(signature),
      },
      encryptionOptions: const EncryptionOptions.values({'assinatura': true}),
    );
  }

  Future<TradeHistory> getOpenPositions({required String path}) async {
    final response = await _request.get(path);
    return TradeHistory.fromJson(response.data);
  }

  Future<List<TradeMonthPerformance>> getMonthsWithClosedPositions({required String path}) async {
    final response = await _request.get(path);
    return (response.data['mesesatividade'] as List).map((position) => TradeMonthPerformance.fromJson(position)).toList();
  }

  Future<TradeHistory> getClosedPositionsOfMonth({required String path, required DateTime monthDate}) async {
    final response = await _request.get(
      path,
      queryParameters: {
        'mes': monthDate.month,
        'ano': monthDate.year,
      },
      encryptionOptions: const EncryptionOptions.noEncryption(),
    );
    return TradeHistory.fromJson(response.data);
  }

  Future<TradeRequiredBalanceCalculation> getRequiredBalanceForAmount({
    required String path,
    required MapEntry<String, int> tradeIdEntry,
    required double amount,
    bool isActive = false,
  }) async {
    final data = <String, dynamic>{
      'valorPorRecomendacao': amount,
      'tipoOperacao': isActive ? 2 : 1, // 1 - Ativação, 2 - Edição
    }..addEntries([tradeIdEntry]);
    final response = await _request.post(path, data, encryptionOptions: const EncryptionOptions.noEncryption());
    return TradeRequiredBalanceCalculation.fromJson(response.data);
  }
}
