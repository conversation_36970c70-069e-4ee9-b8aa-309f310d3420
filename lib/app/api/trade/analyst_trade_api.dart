import 'package:get/get.dart';

import '../../models/trade/analyst/analyst_trade_history.dart';
import '../../models/trade/analyst/analyst_trade_list.dart';
import '../../models/trade/trade.dart';
import '../../models/trade/trade_history.dart';
import '../../models/trade/trade_month_performance.dart';
import '../../models/trade/trade_required_balance_calculation.dart';
import 'trade_api.dart';

class AnalystTradeApi {
  final _tradeApi = Get.find<TradeApi>();

  Future<Trade> getAnalystById(String id) async {
    final response = await _tradeApi.getTradeById(path: '/TradeAnalista/analista/$id');
    return Trade.fromJson(response.data);
  }

  Future<AnalystTradeList> getAnalysts() async {
    final response = await _tradeApi.getTrades(path: '/TradeAnalista/analistas');
    return AnalystTradeList.fromJson(response.data);
  }

  Future<List<TradeMonthPerformance>> getAnalystLastYearHistory({required int analystId}) {
    return _tradeApi.getTradeLastYearHistory(path: '/TradeAnalista/resumoMensal/$analystId');
  }

  Future<AnalystTradeHistory> getAnalystMonthHistory({required int analystId, required DateTime monthDate}) async {
    final response = await _tradeApi.getTradeMonthHistory(path: '/TradeAnalista/historico/$analystId', monthDate: monthDate);
    return AnalystTradeHistory.fromJson(response.data);
  }

  Future<void> hireAnalyst({
    required int analystId,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) {
    return _tradeApi.hireTrade(
      path: '/TradeAnalista/$analystId',
      amount: amount,
      signature: signature,
      didAcceptTerms: didAcceptTerms,
      isProfileSuitabilityUnfit: isProfileSuitabilityUnfit,
      contractCount: contractCount,
    );
  }

  Future<void> editAnalyst({
    required int analystId,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) {
    return _tradeApi.editTrade(
      path: '/TradeAnalista/editar/$analystId',
      amount: amount,
      signature: signature,
      didAcceptTerms: didAcceptTerms,
      isProfileSuitabilityUnfit: isProfileSuitabilityUnfit,
      contractCount: contractCount,
    );
  }

  Future<void> deactivateAnalyst({
    required int analystId,
    required List<List<int>> signature,
  }) {
    return _tradeApi.deactivateTrade(
      path: '/TradeAnalista/desativar/$analystId',
      signature: signature,
    );
  }

  Future<TradeHistory> getAnalystOpenPositions({required int analystId}) {
    return _tradeApi.getOpenPositions(path: '/TradeAnalista/operacoesEmAberto/$analystId');
  }

  Future<List<TradeMonthPerformance>> getAnalystMonthsWithClosedPositions({required int analystId}) {
    return _tradeApi.getMonthsWithClosedPositions(path: '/TradeAnalista/resumoMensalEncerradas/$analystId');
  }

  Future<TradeHistory> getAnalystClosedPositionsOfMonth({
    required int analystId,
    required DateTime monthDate,
  }) {
    return _tradeApi.getClosedPositionsOfMonth(path: '/TradeAnalista/operacoesEncerradas/$analystId', monthDate: monthDate);
  }

  Future<TradeRequiredBalanceCalculation> getAnalystRequiredBalanceForAmount({
    required int analystId,
    required double amount,
    bool isActive = false,
  }) {
    return _tradeApi.getRequiredBalanceForAmount(
      path: '/TradeAnalista/obterValorGarantia',
      tradeIdEntry: MapEntry('analistaId', analystId),
      amount: amount,
      isActive: isActive,
    );
  }
}
