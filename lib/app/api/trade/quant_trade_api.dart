import 'package:get/get.dart';

import '../../models/trade/quant/quant_trade_list.dart';
import '../../models/trade/quant/quant_trade_history.dart';
import '../../models/trade/quant/quant_trade.dart';
import '../../models/trade/trade.dart';
import '../../models/trade/trade_history.dart';
import '../../models/trade/trade_month_performance.dart';
import '../../models/trade/trade_required_balance_calculation.dart';
import 'trade_api.dart';

class QuantTradeApi {
  final _tradeApi = Get.find<TradeApi>();

  Future<Trade> getStrategyById(String id) async {
    final response = await _tradeApi.getTradeById(path: '/TradeQuantitativo/estrategia/$id');
    return QuantTrade.fromJson(response.data);
  }

  Future<QuantTradeList> getStrategies() async {
    final response = await _tradeApi.getTrades(path: '/TradeQuantitativo/estrategias');
    return QuantTradeList.fromJson(response.data);
  }

  Future<List<TradeMonthPerformance>> getStrategyLastYearHistory({required int strategyId}) {
    return _tradeApi.getTradeLastYearHistory(path: '/TradeQuantitativo/resumoMensal/$strategyId');
  }

  Future<QuantTradeHistory> getStrategyMonthHistory({required int strategyId, required DateTime monthDate}) async {
    final response = await _tradeApi.getTradeMonthHistory(path: '/TradeQuantitativo/historico/$strategyId', monthDate: monthDate);
    return QuantTradeHistory.fromJson(response.data);
  }

  Future<void> hireStrategy({
    required int strategyId,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
  }) {
    return _tradeApi.hireTrade(
      path: '/TradeQuantitativo/$strategyId',
      amount: amount,
      signature: signature,
      didAcceptTerms: didAcceptTerms,
      isProfileSuitabilityUnfit: isProfileSuitabilityUnfit,
    );
  }

  Future<void> editStrategy({
    required int strategyId,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
  }) {
    return _tradeApi.hireTrade(
      path: '/TradeQuantitativo/editar/$strategyId',
      amount: amount,
      signature: signature,
      didAcceptTerms: didAcceptTerms,
      isProfileSuitabilityUnfit: isProfileSuitabilityUnfit,
    );
  }

  Future<void> deactivateStrategy({
    required int strategyId,
    required List<List<int>> signature,
  }) {
    return _tradeApi.deactivateTrade(
      path: '/TradeQuantitativo/desativar/$strategyId',
      signature: signature,
    );
  }

  Future<TradeHistory> getStrategyOpenPositions({required int strategyId}) {
    return _tradeApi.getOpenPositions(path: '/TradeQuantitativo/operacoesEmAberto/$strategyId');
  }

  Future<List<TradeMonthPerformance>> getStrategyMonthsWithClosedPositions({required int strategyId}) {
    return _tradeApi.getMonthsWithClosedPositions(path: '/TradeQuantitativo/resumoMensalEncerradas/$strategyId');
  }

  Future<TradeHistory> getStrategyClosedPositionsOfMonth({
    required int strategyId,
    required DateTime monthDate,
  }) {
    return _tradeApi.getClosedPositionsOfMonth(path: '/TradeQuantitativo/operacoesEncerradas/$strategyId', monthDate: monthDate);
  }

  Future<TradeRequiredBalanceCalculation> getStrategyRequiredBalanceForAmount({
    required int strategyId,
    required double amount,
    bool isActive = false,
  }) {
    return _tradeApi.getRequiredBalanceForAmount(
      path: '/TradeQuantitativo/obterValorGarantia',
      tradeIdEntry: MapEntry('estrategiaId', strategyId),
      amount: amount,
      isActive: isActive,
    );
  }
}
