import 'package:get/get.dart';

import '../../models/trade/partner/partner_trade_list.dart';
import '../../models/trade/trade.dart';
import '../../models/trade/trade_history.dart';
import '../../models/trade/trade_month_performance.dart';
import '../../models/trade/trade_required_balance_calculation.dart';
import 'trade_api.dart';

class PartnerTradeApi {
  final _tradeApi = Get.find<TradeApi>();

  Future<Trade> getPartnerById(String id) async {
    final response = await _tradeApi.getTradeById(path: '/TradeParceiro/analista/$id');
    return Trade.fromJson(response.data);
  }

  Future<PartnerTradeList> getPartners() async {
    final response = await _tradeApi.getTrades(path: '/TradeParceiro/analistas');
    return PartnerTradeList.fromJson(response.data);
  }

  Future<List<TradeMonthPerformance>> getPartnerLastYearHistory({required int partnerId}) {
    return _tradeApi.getTradeLastYearHistory(path: '/TradeParceiro/resumoMensal/$partnerId');
  }

  Future<TradeHistory> getPartnerMonthHistory({required int partnerId, required DateTime monthDate}) async {
    final response = await _tradeApi.getTradeMonthHistory(path: '/TradeParceiro/historico/$partnerId', monthDate: monthDate);
    return TradeHistory.fromJson(response.data);
  }

  Future<void> hirePartner({
    required int partnerId,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) {
    return _tradeApi.hireTrade(
      path: '/TradeParceiro/$partnerId',
      amount: amount,
      signature: signature,
      didAcceptTerms: didAcceptTerms,
      isProfileSuitabilityUnfit: isProfileSuitabilityUnfit,
      contractCount: contractCount,
    );
  }

  Future<void> editPartner({
    required int partnerId,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) {
    return _tradeApi.editTrade(
      path: '/TradeParceiro/editar/$partnerId',
      amount: amount,
      signature: signature,
      didAcceptTerms: didAcceptTerms,
      isProfileSuitabilityUnfit: isProfileSuitabilityUnfit,
      contractCount: contractCount,
    );
  }

  Future<void> deactivatePartner({
    required int partnerId,
    required List<List<int>> signature,
  }) {
    return _tradeApi.deactivateTrade(
      path: '/TradeParceiro/desativar/$partnerId',
      signature: signature,
    );
  }

  Future<TradeHistory> getPartnerOpenPositions({required int partnerId}) {
    return _tradeApi.getOpenPositions(path: '/TradeParceiro/operacoesEmAberto/$partnerId');
  }

  Future<List<TradeMonthPerformance>> getPartnerMonthsWithClosedPositions({required int partnerId}) {
    return _tradeApi.getMonthsWithClosedPositions(path: '/TradeParceiro/resumoMensalEncerradas/$partnerId');
  }

  Future<TradeHistory> getPartnerClosedPositionsOfMonth({
    required int partnerId,
    required DateTime monthDate,
  }) {
    return _tradeApi.getClosedPositionsOfMonth(path: '/TradeParceiro/operacoesEncerradas/$partnerId', monthDate: monthDate);
  }

  Future<TradeRequiredBalanceCalculation> getPartnerRequiredBalanceForAmount({
    required int partnerId,
    required double amount,
    bool isActive = false,
  }) {
    return _tradeApi.getRequiredBalanceForAmount(
      path: '/TradeParceiro/obterValorGarantiaRadar',
      tradeIdEntry: MapEntry('analistaId', partnerId),
      amount: amount,
      isActive: isActive,
    );
  }
}
