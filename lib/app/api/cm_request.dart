import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:encrypt/encrypt.dart';
import 'package:flutter/services.dart';
import 'package:pointycastle/asymmetric/api.dart';

import '../errors/error_handlers.dart';
import '../errors/exceptions.dart';
import '../widgets/modal/cm_dialog.dart';

class CMRequest {
  CMRequest(String baseUrl)
      : _dio = Dio(
          BaseOptions(
            baseUrl: baseUrl,
            contentType: 'application/json',
            responseType: ResponseType.json,
            connectTimeout: const Duration(seconds: 180),
            receiveTimeout: const Duration(seconds: 180),
            sendTimeout: const Duration(seconds: 180),
          ),
        );

  final Dio _dio;

  final _encrypter = _Encrypter();

  String? token;

  Future<Response<T>> post<T>(
    String resource,
    dynamic payload, {
    Options? options,
    EncryptionOptions encryptionOptions = const EncryptionOptions(),
  }) =>
      _request<T>(
        resource,
        method: HttpMethod.post,
        payload: payload,
        options: options,
        encryptionOptions: encryptionOptions,
      );

  Future<Response<T>> put<T>(
    String resource,
    dynamic payload, {
    Options? options,
    EncryptionOptions encryptionOptions = const EncryptionOptions(),
  }) =>
      _request<T>(
        resource,
        method: HttpMethod.put,
        payload: payload,
        options: options,
        encryptionOptions: encryptionOptions,
      );

  Future<Response<T>> delete<T>(
    String resource,
    dynamic payload, {
    Options? options,
    EncryptionOptions encryptionOptions = const EncryptionOptions(),
  }) =>
      _request<T>(
        resource,
        method: HttpMethod.delete,
        payload: payload,
        options: options,
        encryptionOptions: encryptionOptions,
      );

  Future<Response<T>> get<T>(
    String resource, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    EncryptionOptions encryptionOptions = const EncryptionOptions(),
  }) =>
      _request<T>(
        resource,
        method: HttpMethod.get,
        queryParameters: queryParameters,
        options: options,
        encryptionOptions: encryptionOptions,
      );

  Future<Response<T>> _request<T>(
    String resource, {
    required HttpMethod method,
    dynamic payload,
    Options? options,
    Map<String, dynamic>? queryParameters,
    EncryptionOptions encryptionOptions = const EncryptionOptions(),
  }) async {
    if (payload != null) log('$resource payload: $payload', name: 'CMRequest');
    if (payload != null && encryptionOptions.shouldEncryptPayload) {
      payload = await _encrypter.encryptPayload(payload, options: encryptionOptions);
      log('$resource encrypted payload: $payload', name: 'CMRequest');
    }

    if (queryParameters != null) log('$resource queryParameters: $queryParameters', name: 'CMRequest');
    if (queryParameters != null && encryptionOptions.shouldEncryptPayload) {
      queryParameters = await _encrypter.encryptMapValues(queryParameters);
      log('$resource encrypted queryParameters: $payload', name: 'CMRequest');
    }

    options = (options ?? Options()).copyWith(
      method: method.name,
      headers: {
        if (token != null) HttpHeaders.authorizationHeader: 'Bearer $token',
        ...?options?.headers,
        'From': 'app',
      },
    );

    try {
      final response = await _dio.request<T>(
        resource,
        data: payload,
        options: options,
        queryParameters: queryParameters,
      );
      log('$resource response: $response', name: 'CMRequest');
      return response;
    } on DioException catch (error) {
      if (error.response != null) {
        log('$resource error statusCode: ${error.response?.statusCode}', name: 'CMRequest');
        log('$resource error response: ${error.response?.data}', name: 'CMRequest');
      } else {
        log('$resource error: $error', name: 'CMRequest');
      }

      // if ([401, 403].contains(error.response?.statusCode)) {
      //   await showSessionExpiredDialog();
      //   throw SessionExpiredException();
      // }

      rethrow;
    }
  }
}

enum HttpMethod { get, post, put, delete }

class _Encrypter {
  RSAPublicKey? _publicKey;

  Future<dynamic> encryptPayload(dynamic payload, {required EncryptionOptions options}) async {
    if (options.onlyEncryptValues) {
      return await encryptMapValues(payload, options.valuesToEncrypt);
    } else {
      return '"${await encryptString(jsonEncode(payload))}"';
    }
  }

  Future<String> encryptString(String value) async {
    // lazy load public key from file
    _publicKey ??= RSAKeyParser().parse(await rootBundle.loadString('assets/api/public_key.pem')) as RSAPublicKey;

    final encrypter = Encrypter(RSA(publicKey: _publicKey));

    try {
      return encrypter.encryptBytes(const Latin1Encoder().convert(value)).base64;
    } catch (error) {
      CMDialog.warning(
        description: 'Remova acentos ou caracteres especiais e tente novamente.',
        title: 'Caracteres inválidos',
        showCancelButton: false,
        confirmButtonText: 'OK',
      ).show();
      rethrow;
    }
  }

  Future<Map<String, dynamic>> encryptMapValues(
    dynamic payload, [
    Map<String, bool>? valuesToEncrypt,
  ]) async {
    Map<String, dynamic> encryptedPayload = {};

    await Future.forEach(payload.entries, (MapEntry<String, dynamic> entry) async {
      if (valuesToEncrypt == null || valuesToEncrypt[entry.key] == true) {
        encryptedPayload[entry.key] = await encryptString(
          entry.value is String ? entry.value : jsonEncode(entry.value),
        );
      } else {
        encryptedPayload[entry.key] = entry.value;
      }
    });

    return encryptedPayload;
  }
}

class EncryptionOptions {
  final bool shouldEncryptPayload;
  final bool onlyEncryptValues;
  final Map<String, bool>? valuesToEncrypt;

  const EncryptionOptions({
    this.shouldEncryptPayload = true,
    this.onlyEncryptValues = false,
    this.valuesToEncrypt,
  });

  const EncryptionOptions.payload()
      : shouldEncryptPayload = true,
        onlyEncryptValues = false,
        valuesToEncrypt = null;

  const EncryptionOptions.values([this.valuesToEncrypt])
      : shouldEncryptPayload = true,
        onlyEncryptValues = true;

  const EncryptionOptions.noEncryption()
      : shouldEncryptPayload = false,
        onlyEncryptValues = false,
        valuesToEncrypt = null;
}
