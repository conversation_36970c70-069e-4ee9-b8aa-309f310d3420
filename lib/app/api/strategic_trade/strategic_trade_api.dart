import 'package:get/get.dart';

import '../../models/strategic_trade/strategic_trade.dart';
import '../cm_request.dart';

class StrategicTradeApi {
  final _request = Get.find<CMRequest>();

  Future<List<StrategicTrade>> getStrategies() async {
    final response = await _request.get('/TradeEstrategico');
    return (response.data as List).map((titulo) => StrategicTrade.fromJson(titulo)).toList();
  }
}
