import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

import '../../api/shared/shared_api.dart';
import '../../api/strategic_trade/strategic_trade_api.dart';
import '../../api/suitability/suitability_api.dart';
import '../../api/usuario/usuario_api.dart';
import '../../models/strategic_trade/strategic_trade.dart';
import '../../pages/help_page.dart';
import '../../pages/profile/suitability_page.dart';
import '../../utils/get_file.dart';
import '../../utils/integrations.dart';
import '../../widgets/modal/cm_bottom_sheet.dart';
import '../user_controller.dart';

class StrategicInformationPageController extends GetxController with GetSingleTickerProviderStateMixin {
  final _sharedApi = Get.find<SharedApi>();
  final _strategicTradeApi = Get.find<StrategicTradeApi>();
  final _userController = Get.find<UserController>();
  final _usuarioApi = Get.find<UsuarioApi>();

  final suitabilityProfile = Rx<UserSuitabilityProfile?>(null);
  final distributorFeeText = RxnString();
  final selectedStrategic = Rx<StrategicTrade?>(null);
  bool isClientBlocked = false;
  int? _strategicId;

  @override
  void onInit() {
    super.onInit();
    if (Get.arguments case int id) {
      _strategicId = id;
    }
  }

  Future<bool> loadStrategicTrade() async {
    final response = await _strategicTradeApi.getStrategies();
    selectedStrategic.value = response.firstWhere((x) => x.id == _strategicId);

    await _getDistributorFeeText();
    await _getClientInfo();

    update();
    return true;
  }

  Future<void> onWhatsappButtonPressed() async {
    if ((suitabilityProfile.value?.text ?? '') != UserSuitabilityProfile.aggressive.text) {
      await _showInvestorProfileWarning();
    } else if (isClientBlocked) {
      await _showInvestorBlockedWarning(_userController.firstName ?? '');
    } else {
      sendWhatsAppMessageStrategicTrade(selectedStrategic.value?.title ?? '');
    }
  }

  Future<void> _showInvestorProfileWarning() async {
    await CMBottomSheet.choice(
      topWidget: Image.asset('assets/icons/dialog/noncompliance_profile.png'),
      title: 'Perfil do investidor incompatível',
      description:
          'No momento este produto é destinado apenas aos investidores com perfil Agressivo. Caso tenha interesse em reavaliar seu perfil, confira a página Perfil do Investidor',
      topButtonText: 'Perfil do Investidor',
      topButtonOnPressed: () async {
        await Get.toNamed(SuitabilityPage.routeName);
        await loadStrategicTrade();
      },
      bottomButtonText: 'Fechar',
      bottomButtonOnPressed: Get.back,
    ).show();
  }

  Future _showInvestorBlockedWarning(String userName) async {
    await CMBottomSheet.choice(
      topWidget: Image.asset('assets/icons/dialog/investment_cancelled.png'),
      title: 'Aviso',
      description: '$userName, sua conta está bloqueada para fazer investimentos, netre em contato com o atendimento',
      topButtonText: 'Central de Ajuda',
      topButtonOnPressed: () => Get.toNamed(HelpPage.routeName, arguments: true),
      bottomButtonText: 'Fechar',
      bottomButtonOnPressed: Get.back,
    ).show();
  }

  Future<void> onOpenDocumentNullWarnning() async {
    await CMBottomSheet.simple(
      title: 'Erro',
      description: 'Não foi possível baixar o documento.',
      buttonText: 'FECHAR',
    ).show();
  }

  Future<void> _getClientInfo() async {
    var user = await _usuarioApi.usuarioDados();
    isClientBlocked = user.clienteStatus != 1;
    suitabilityProfile.value = user.suitabilityEnum;
  }

  Future<void> _getDistributorFeeText() async {
    try {
      distributorFeeText.value = await _sharedApi.getDistributorFeeText(id: 7);
    } catch (error) {
      distributorFeeText.value = null;
    }
  }

  void openFile(
    String fileUrl,
    String fileName,
  ) =>
      downloadAndOpenFile(
        fileUrl,
        '$fileName.pdf',
        showLoadingOverlay: true,
      );
}
