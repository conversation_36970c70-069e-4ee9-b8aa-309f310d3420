import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

import '../../api/strategic_trade/strategic_trade_api.dart';
import '../../api/suitability/suitability_api.dart';
import '../../api/usuario/usuario_api.dart';
import '../../models/strategic_trade/strategic_trade.dart';
import '../../pages/help_page.dart';
import '../../pages/profile/suitability_page.dart';
import '../../pages/strategic_trade/strategic_Information_page.dart';
import '../../utils/integrations.dart';
import '../../widgets/modal/cm_bottom_sheet.dart';
import '../user_controller.dart';

class StrategicListPageController extends GetxController with GetSingleTickerProviderStateMixin {
  final _strategicTradeApi = Get.find<StrategicTradeApi>();
  final _userController = Get.find<UserController>();
  final _usuarioApi = Get.find<UsuarioApi>();

  final suitabilityProfile = Rx<UserSuitabilityProfile?>(null);
  var strategicList = <StrategicTrade>[];
  bool isClientBlocked = false;

  Future<bool> loadStrategicTrade() async {
    strategicList = await _strategicTradeApi.getStrategies();

    // Filtra lista retornada
    strategicList = strategicList.where((item) {
      // Filtra apenas publicados
      final isPublished = (item.status ?? '').toLowerCase() == 'publicado';
      if (!isPublished) return false;

      // Filtra apenas os que não estão vencidos
      final isNotExpired = item.dueDate == null || item.dueDate!.isAfter(DateTime.now());
      return isNotExpired;
    }).toList();

    // Ordena por data de publicação
    strategicList.sort((a, b) => a.publication != null && b.publication != null ? b.publication!.compareTo(a.publication!) : 0);

    await _getClientInfo();

    update();
    return true;
  }

  Future<void> onWhatsappButtonPressed(String operation) async {
    if ((suitabilityProfile.value?.text ?? '') != UserSuitabilityProfile.aggressive.text) {
      await _showInvestorProfileWarning();
    } else if (isClientBlocked) {
      await _showInvestorBlockedWarning(_userController.firstName ?? '');
    } else {
      sendWhatsAppMessageStrategicTrade(operation);
    }
  }

  Future<void> _showInvestorProfileWarning() async {
    await CMBottomSheet.choice(
      topWidget: Image.asset('assets/icons/dialog/noncompliance_profile.png'),
      title: 'Perfil do investidor incompatível',
      description:
          'No momento este produto é destinado apenas aos investidores com perfil Agressivo. Caso tenha interesse em reavaliar seu perfil, confira a página Perfil do Investidor',
      topButtonText: 'Perfil do Investidor',
      topButtonOnPressed: () async {
        await Get.toNamed(SuitabilityPage.routeName);
        await loadStrategicTrade();
      },
      bottomButtonText: 'Fechar',
      bottomButtonOnPressed: Get.back,
    ).show();
  }

  Future _showInvestorBlockedWarning(String userName) async {
    await CMBottomSheet.choice(
      topWidget: Image.asset('assets/icons/dialog/investment_cancelled.png'),
      title: 'Aviso',
      description: '$userName, sua conta está bloqueada para fazer investimentos, netre em contato com o atendimento',
      topButtonText: 'Central de Ajuda',
      topButtonOnPressed: () => Get.toNamed(HelpPage.routeName, arguments: true),
      bottomButtonText: 'Fechar',
      bottomButtonOnPressed: Get.back,
    ).show();
  }

  Future<void> _getClientInfo() async {
    var user = await _usuarioApi.usuarioDados();
    isClientBlocked = user.clienteStatus != 1;
    suitabilityProfile.value = user.suitabilityEnum;
  }

  void showDetails(int id) {
    Get.toNamed(
      StrategicInformationPage.routeName,
      arguments: id,
    );
  }
}
