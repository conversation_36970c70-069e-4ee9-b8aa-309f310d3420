import 'package:get/get.dart';

import '../../api/usuario/usuario_api.dart';
import '../../models/user_contact_data.dart';
import '../../utils/extensions.dart';
import '../biometrics_controller.dart';
import '../user_controller.dart';

class AccountSecurityPageController extends GetxController {
  final _userController = Get.find<UserController>();
  final _usuarioApi = Get.find<UsuarioApi>();
  final _biometricsController = BiometricsController();

  // Contato
  bool isFetchingUserContactData = false;
  bool didThrowUserContactDataError = false;
  UserContactData? _userContactData;
  String get userName => _userController.firstName ?? 'Cliente';
  String get userEmail => _userContactData?.email ?? 'Indefinido';
  String get userPhoneNumber => _userContactData?.phoneNumber?.brazilianPhoneCodeRemoved.maskedPhoneNumber ?? 'Indefinido';

  // Biometria
  bool isFetchingBiometricsData = false;
  bool didThrowBiometricsDataError = false;
  bool get isBiometricsEnabled => _biometricsController.uuid?.isNotEmpty == true;

  @override
  void onInit() {
    super.onInit();
    fetchUserContactData();
    fetchBiometricsData();
  }

  Future<void> fetchUserContactData() async {
    isFetchingUserContactData = true;
    update();
    try {
      _userContactData = await _usuarioApi.getUserContactData();
      didThrowUserContactDataError = false;
    } catch (error) {
      didThrowUserContactDataError = true;
    } finally {
      isFetchingUserContactData = false;
      update();
    }
  }

  Future<void> fetchBiometricsData() async {
    isFetchingBiometricsData = true;
    update();
    try {
      await _biometricsController.getUserBiometricsData();
      didThrowBiometricsDataError = false;
    } catch (error) {
      didThrowBiometricsDataError = true;
    } finally {
      isFetchingBiometricsData = false;
      update();
    }
  }

  void onBiometricsSwitchChanged(_) {
    if (_biometricsController.uuid == null) {
      _biometricsController.showActionConfirmationDialog('Cadastrar', _biometricsController.registerBiometrics);
    } else {
      _biometricsController.showActionConfirmationDialog('Remover', _biometricsController.removeBiometrics);
    }
  }
}
