import 'package:get/get.dart';

import '../../../api/trade/quant_trade_api.dart';
import '../../../models/trade/trade.dart';
import '../../../pages/trade/quant/quant_trade_description_page.dart';
import '../../../pages/trade/quant/quant_trade_hired_history_page.dart';
import '../../../pages/trade/quant/quant_trade_hiring_page.dart';
import '../../../pages/trade/quant/quant_trade_how_it_works_page.dart';
import '../../../pages/trade/quant/quant_trade_unhired_history_month_page.dart';
import '../../../pages/trade/quant/quant_trade_unhired_history_page.dart';
import '../../../utils/extensions.dart';
import '../trade_hiring_controller.dart';
import '../trade_history_controller.dart';
import '../trade_list_page_controller.dart';

class QuantTradeListPageController extends TradeListPageController {
  final _quantTradeApi = Get.find<QuantTradeApi>();

  @override
  String get pageTitle => 'Trade Quant';

  @override
  String get availableTradesTabText => 'Estratégias\ndisponíveis';

  @override
  String get hiredTradesTabText => 'Estratégias\ncontratadas';

  @override
  void showInfoPage() {
    Get.toNamed(QuantTradeHowItWorksPage.routeName);
  }

  @override
  void showProfilePage(Trade trade) => Get.toNamed(QuantTradeDescriptionPage.routeName, arguments: trade);

  @override
  void showHistoryPage(Trade trade) {
    TradeHistoryController.tryNavigatingToHistoryPage(
      trade: trade,
      unhiredHistoryRouteName: QuantTradeUnhiredHistoryPage.routeName,
      unhiredHistoryMontRouteName: QuantTradeUnhiredHistoryMonthPage.routeName,
      hiredHistoryRouteName: QuantTradeHiredHistoryPage.routeName,
    );
  }

  @override
  void showHiringPage(Trade trade) {
    TradeHiringController.tryNavigatingToHiringPage(
      trade: trade,
      routeName: QuantTradeHiringPage.routeName,
    );
  }

  @override
  Future<(List<Trade>, List<Trade>)> getTrades() async {
    final response = await _quantTradeApi.getStrategies();
    return (
      response.availableStrategies.sortedByPerformance,
      response.hiredStrategies,
    );
  }

  @override
  String get deactivatedText => 'A Estratégia não está mais disponível';

  @override
  String getResultAmountMaskedText(Trade trade) {
    return trade.performance.toMaskedAmount(
      mask: trade.isBmf ? AmountMask.points : AmountMask.percentage,
      showPlusSign: true,
    );
  }

  @override
  String getResultSubtitle(Trade trade) => trade.isHired ? 'Seu resultado bruto' : 'Resultado bruto';

  @override
  String getResultTooltipText(Trade trade) => 'Resultado bruto dos últimos 12 meses'
      '\nou desde o início das recomendações,'
      '\nconsiderando o valor ${trade.isHired ? 'aplicado' : 'mínimo aplicável'}.';
}
