import 'package:get/get.dart';

import '../../../api/trade/quant_trade_api.dart';
import '../../../models/trade/quant/quant_trade_history.dart';
import '../../../pages/trade/quant/quant_trade_hiring_page.dart';
import '../../../pages/trade/quant/quant_trade_unhired_history_month_detail_page.dart';
import '../trade_unhired_history_month_page_controller.dart';

class QuantTradeUnhiredHistoryMonthPageController extends TradeUnhiredHistoryMonthPageController {
  final _quantTradeApi = Get.find<QuantTradeApi>();

  @override
  String get tradeLabel => 'Estratégia';

  @override
  String get monthDetailRouteName => QuantTradeUnhiredHistoryMonthDetailPage.routeName;

  @override
  Future<QuantTradeHistory> getTradeMonthHistory({required int id, required DateTime monthDate}) {
    return _quantTradeApi.getStrategyMonthHistory(strategyId: id, monthDate: monthDate);
  }

  @override
  String get informationText =>
      'O resultado do mês da estratégia é somado contando todas as recomendações emitidas no mês de referência. Se não houver saldo para realizar alguma das operações do mês, seu resultado (%) poderá ser maior ou menor do que o resultado (%) da estratégia. As operações são executadas automaticamente em ordem cronológica.';

  @override
  String get hiringRouteName => QuantTradeHiringPage.routeName;
}
