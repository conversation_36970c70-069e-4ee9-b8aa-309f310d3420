import 'package:get/get.dart';

import '../../../models/trade/trade.dart';
import '../../../models/trade/trade_required_balance_calculation.dart';
import '../../../pages/tabs/my_investments/my_investments.dart';
import '../../../api/trade/quant_trade_api.dart';
import '../../../pages/trade/quant/quant_trade_description_page.dart';
import '../../../pages/trade/quant/quant_trade_hired_history_page.dart';
import '../../../pages/trade/quant/quant_trade_list_page.dart';
import '../../../pages/trade/quant/quant_trade_how_it_works_page.dart';
import '../../../pages/trade/quant/quant_trade_minimum_balance_page.dart';
import '../../../pages/trade/quant/quant_trade_unhired_history_month_page.dart';
import '../../../pages/trade/quant/quant_trade_unhired_history_page.dart';
import 'quant_trade_list_page_controller.dart';
import '../trade_hiring_page_controller.dart';
import '../trade_history_controller.dart';
import '../trade_list_page_controller.dart';

class QuantTradeHiringPageController extends TradeHiringPageController {
  final _quantTradeApi = Get.find<QuantTradeApi>();

  @override
  Future<TradeRequiredBalanceCalculation> getRequiredBalance({required int id, required double amount, required bool isActive}) {
    return _quantTradeApi.getStrategyRequiredBalanceForAmount(strategyId: id, amount: amount, isActive: isActive);
  }

  @override
  String get serviceTitle => 'Trade Quant';

  @override
  Future<void> editTrade({
    required int id,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) {
    return _quantTradeApi.editStrategy(strategyId: id, amount: amount, signature: signature, didAcceptTerms: didAcceptTerms, isProfileSuitabilityUnfit: isProfileSuitabilityUnfit);
  }

  @override
  Future<void> hireTrade({
    required int id,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) {
    return _quantTradeApi.hireStrategy(strategyId: id, amount: amount, signature: signature, didAcceptTerms: didAcceptTerms, isProfileSuitabilityUnfit: isProfileSuitabilityUnfit);
  }

  @override
  String get tradeReferenceText => 'pela estratégia';

  @override
  Future<void> deactivateTrade({required int id, required List<List<int>> signature}) {
    return _quantTradeApi.deactivateStrategy(strategyId: id, signature: signature);
  }

  @override
  String get purchaseListPageRouteName => QuantTradeListPage.routeName;

  @override
  String get myInvestmentsListPageRouteName => QuantTradeMyListPage.routeName;

  @override
  bool get isListPageControllerRegistered => Get.isRegistered<QuantTradeListPageController>();

  @override
  TradeListPageController get listPageController => Get.find<QuantTradeListPageController>();

  @override
  String get tradeTile => 'Estratégia';

  @override
  int get tradeTermsId => 34;

  @override
  int get distributorFeeTextId => 2;

  @override
  void showHistoryPage(Trade trade) {
    TradeHistoryController.tryNavigatingToHistoryPage(
      trade: trade,
      unhiredHistoryRouteName: QuantTradeUnhiredHistoryPage.routeName,
      unhiredHistoryMontRouteName: QuantTradeUnhiredHistoryMonthPage.routeName,
      hiredHistoryRouteName: QuantTradeHiredHistoryPage.routeName,
    );
  }

  @override
  String get howItWorksPageRouteName => QuantTradeHowItWorksPage.routeName;

  @override
  String get descriptionPageRouteName => QuantTradeDescriptionPage.routeName;

  @override
  String get minimumBalancePageRouteName => QuantTradeMinimumBalancePage.routeName;
}
