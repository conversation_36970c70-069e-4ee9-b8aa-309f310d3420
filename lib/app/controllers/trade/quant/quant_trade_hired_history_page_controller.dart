import 'package:get/get.dart';

import '../../../api/trade/quant_trade_api.dart';
import '../../../models/trade/trade_history.dart';
import '../../../models/trade/trade_month_performance.dart';
import '../../../pages/trade/quant/quant_trade_hired_history_month_page.dart';
import '../../../pages/trade/quant/quant_trade_hired_history_open_position_detail_page.dart';
import '../../../pages/trade/quant/quant_trade_hiring_page.dart';
import '../../../utils/extensions.dart';
import '../trade_hired_history_page_controller.dart';

class QuantTradeHiredHistoryPageController extends TradeHiredHistoryPageController {
  final _quantTradeApi = Get.find<QuantTradeApi>();

  @override
  String get tradeLabel => 'Estratégia';

  @override
  String get performanceLabel => 'Resultado bruto desde a primeira ativação';

  @override
  AmountMask get performanceMask => trade.isBmf ? AmountMask.points : AmountMask.percentage;

  @override
  String get editingRouteName => QuantTradeHiringPage.routeName;

  @override
  String get closedPositionsMonthRouteName => QuantTradeHiredHistoryMonthPage.routeName;

  @override
  String get openPositionDetailRouteName => QuantTradeHiredHistoryOpenPositionDetailPage.routeName;

  @override
  Future<TradeHistory> getTradeOpenPositions({required int id}) {
    return _quantTradeApi.getStrategyOpenPositions(strategyId: id);
  }

  @override
  Future<List<TradeMonthPerformance>> getTradeMonthsWithClosedPositions({required int id}) {
    return _quantTradeApi.getStrategyMonthsWithClosedPositions(strategyId: id);
  }

  @override
  String get closedPositionsMonthPerformanceLabel => 'Gain/Loss';

  @override
  AmountMask get closedPositionsMonthPerformanceMask => trade.isBmf ? AmountMask.points : AmountMask.percentage;
}
