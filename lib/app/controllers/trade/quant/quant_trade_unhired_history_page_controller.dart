import 'package:get/get.dart';

import '../../../api/trade/quant_trade_api.dart';
import '../../../models/trade/trade_month_performance.dart';
import '../../../pages/trade/quant/quant_trade_description_page.dart';
import '../../../pages/trade/quant/quant_trade_hiring_page.dart';
import '../../../pages/trade/quant/quant_trade_unhired_history_month_page.dart';
import '../trade_unhired_history_page_controller.dart';

class QuantTradeUnhiredHistoryPageController extends TradeUnhiredHistoryPageController {
  final _quantTradeApi = Get.find<QuantTradeApi>();

  @override
  String get tradeLabel => 'Estratégia';

  @override
  String get profileButtonLabel => 'Perfil da Estratégia';

  @override
  String get profileRouteName => QuantTradeDescriptionPage.routeName;

  @override
  String get hiringRouteName => QuantTradeHiringPage.routeName;

  @override
  String get monthHistoryRouteName => QuantTradeUnhiredHistoryMonthPage.routeName;

  @override
  Future<List<TradeMonthPerformance>> getTradeLastYearHistory({required int id}) {
    return _quantTradeApi.getStrategyLastYearHistory(strategyId: id);
  }
}
