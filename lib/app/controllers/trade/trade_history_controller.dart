import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../models/trade/trade.dart';
import '../../utils/extensions.dart';
import '../../widgets/modal/cm_bottom_sheet.dart';

abstract class TradeHistoryController extends GetxController {
  final Trade trade = Get.arguments;

  /// Navega para a tela de histórico.
  ///
  /// Caso não haja histórico, exibe um modal informando o usuário. Caso haja, navega para a tela correta, passando [trade] como argumento e retira as já existentes da pilha.
  static void tryNavigatingToHistoryPage({
    required Trade trade,
    required String unhiredHistoryRouteName,
    required String unhiredHistoryMontRouteName,
    required String hiredHistoryRouteName,
    String noHistoryRecommendation = ' Por enquanto você pode conferir as recomendações em aberto na área Recomendações.',
  }) {
    if (!trade.isHired && !trade.hasHistory) {
      CMBottomSheet.simple(
        topWidget: Image.asset('assets/icons/dialog/scheduling_confirmed.png'),
        title: 'Ainda não há histórico',
        description: 'Em breve as recomendações encerradas aparecerão aqui.$noHistoryRecommendation',
      ).show();
      return;
    }
    final routeToShow = trade.isHired ? hiredHistoryRouteName : unhiredHistoryRouteName;
    Get.popNamedExistent(unhiredHistoryMontRouteName);
    Get.toNamedAndPopExistent(routeToShow, arguments: trade);
  }
}
