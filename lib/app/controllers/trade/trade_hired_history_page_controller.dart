import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../models/trade/trade_history.dart';
import '../../models/trade/trade_month_performance.dart';
import '../../utils/extensions.dart';
import '../../widgets/body/cm_body.dart';
import 'trade_history_controller.dart';

abstract class TradeHiredHistoryPageController extends TradeHistoryController {
  final bodyKey = GlobalKey<CMBodyState>();

  TradeHistory? openPositions;
  final monthsWithClosedPositions = <TradeMonthPerformance>[];

  String get tradeLabel;
  String get performanceLabel;
  AmountMask get performanceMask;
  String get editingRouteName;
  String get closedPositionsMonthRouteName;
  String get openPositionDetailRouteName;
  Future<TradeHistory> getTradeOpenPositions({required int id});
  Future<List<TradeMonthPerformance>> getTradeMonthsWithClosedPositions({required int id});
  String get closedPositionsMonthPerformanceLabel;
  AmountMask get closedPositionsMonthPerformanceMask;

  Future<void> loadData() async {
    await Future.wait([
      _getOpenPositions(),
      _getClosedPositions(),
    ]);
    update();
  }

  Future<void> _getOpenPositions() async {
    openPositions = await getTradeOpenPositions(id: trade.id ?? 0);
  }

  Future<void> _getClosedPositions() async {
    final months = await getTradeMonthsWithClosedPositions(id: trade.id ?? 0);
    monthsWithClosedPositions.assignAll(months);
  }

  void onClosedPositionsMonthPressed(TradeMonthPerformance selectedMonth) {
    Get.toNamed(closedPositionsMonthRouteName, arguments: selectedMonth);
  }
}
