import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../models/trade/trade.dart';
import '../../utils/extensions.dart';
import '../../utils/url_handlers.dart';
import '../../widgets/info/decorated_text.dart';
import '../../widgets/modal/cm_bottom_sheet.dart';

abstract class TradeHiringController extends GetxController {
  final trade = Get.arguments as Trade;

  /// Navega para a tela de contratação.
  ///
  /// Caso não seja permitido a ativação, exibe um modal informando o usuário. Caso haja, navega para a tela correta, passando [trade] como argumento e retira a já existente da pilha.
  static void tryNavigatingToHiringPage({
    required Trade trade,
    required String routeName,
  }) {
    if (!trade.isHired && !trade.isHiringAllowed) {
      CMBottomSheet.choice(
        topWidget: Image.asset('assets/icons/dialog/help.png'),
        title: 'Requisitos de ativação',
        descriptionWidget: DecoratedText(
          'Caro(a) cliente, para ativar as operações automáticas de ${trade.fullName}, é necessário ${trade.hiringRequirementsText} (link abaixo). {Caso tenha realizado a contratação hoje, aguarde até o próximo dia útil para ter a ativação liberada}. Em caso de dúvidas entre em contato com nossos canais de atendimento.',
          textStyle: CMBottomSheet.dialogDescriptionTextStyle,
          decoratedTextStyle: CMBottomSheet.boldDialogDescriptionTextStyle,
        ),
        topButtonText: 'Visitar site do parceiro',
        topButtonOnPressed: () => openUrl(trade.hiringRequirementsUrl ?? ''),
        bottomButtonText: 'Fechar',
        bottomButtonOnPressed: Get.back,
      ).show();
      return;
    }
    Get.toNamedAndPopExistent(routeName, arguments: trade);
  }
}
