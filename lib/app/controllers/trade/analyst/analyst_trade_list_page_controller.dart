import 'package:get/get.dart';

import '../../../api/trade/analyst_trade_api.dart';
import '../../../models/trade/trade.dart';
import '../../../pages/trade/analyst/analyst_trade_description_page.dart';
import '../../../pages/trade/analyst/analyst_trade_hired_history_page.dart';
import '../../../pages/trade/analyst/analyst_trade_hiring_page.dart';
import '../../../pages/trade/analyst/analyst_trade_how_it_works_page.dart';
import '../../../pages/trade/analyst/analyst_trade_unhired_history_month_page.dart';
import '../../../pages/trade/analyst/analyst_trade_unhired_history_page.dart';
import '../../../utils/extensions.dart';
import '../trade_hiring_controller.dart';
import '../trade_history_controller.dart';
import '../trade_list_page_controller.dart';

class AnalystTradeListPageController extends TradeListPageController {
  final _analystTradeApi = Get.find<AnalystTradeApi>();

  @override
  String get pageTitle => 'Trade do Analista';

  @override
  String get availableTradesTabText => 'Analistas disponíveis';

  @override
  String get hiredTradesTabText => 'Analistas contratados';

  @override
  void showInfoPage() {
    Get.toNamed(AnalystTradeHowItWorksPage.routeName);
  }

  @override
  void showProfilePage(Trade trade) => Get.toNamed(AnalystTradeDescriptionPage.routeName, arguments: trade);

  @override
  void showHistoryPage(Trade trade) {
    TradeHistoryController.tryNavigatingToHistoryPage(
      trade: trade,
      unhiredHistoryRouteName: AnalystTradeUnhiredHistoryPage.routeName,
      unhiredHistoryMontRouteName: AnalystTradeUnhiredHistoryMonthPage.routeName,
      hiredHistoryRouteName: AnalystTradeHiredHistoryPage.routeName,
    );
  }

  @override
  void showHiringPage(Trade trade) {
    TradeHiringController.tryNavigatingToHiringPage(
      trade: trade,
      routeName: AnalystTradeHiringPage.routeName,
    );
  }

  @override
  Future<(List<Trade>, List<Trade>)> getTrades() async {
    final response = await _analystTradeApi.getAnalysts();
    return (
      response.availableAnalysts.sortedByPerformance.alteranatedByMarket,
      response.hiredAnalysts.sortedByPerformance.alteranatedByMarket,
    );
  }

  @override
  bool get isShowMoreButtonVisible => false;

  @override
  List<Trade> get relevantAvailableTrades => availableTrades;

  @override
  String get deactivatedText => 'O Analista não está mais disponível';

  @override
  String getResultAmountMaskedText(Trade trade) {
    return trade.performance.toMaskedAmount(
      mask: trade.isBmf ? AmountMask.points : AmountMask.percentage,
      showPlusSign: true,
    );
  }

  @override
  String getResultSubtitle(Trade trade) => trade.isHired ? 'Seu resultado bruto' : 'Resultado bruto';

  @override
  String getResultTooltipText(Trade trade) => 'Resultado bruto dos últimos 12 meses'
      '\nou desde o início das recomendações,'
      '\nconsiderando o valor ${trade.isHired ? 'aplicado' : 'mínimo aplicável'}.';
}
