import 'package:get/get.dart';

import '../../../api/trade/analyst_trade_api.dart';
import '../../../models/trade/analyst/analyst_trade_history.dart';
import '../../../pages/trade/analyst/analyst_trade_hiring_page.dart';
import '../../../pages/trade/analyst/analyst_trade_unhired_history_month_detail_page.dart';
import '../trade_unhired_history_month_page_controller.dart';

class AnalystTradeUnhiredHistoryMonthPageController extends TradeUnhiredHistoryMonthPageController {
  final _analystTradeApi = Get.find<AnalystTradeApi>();

  @override
  String get tradeLabel => 'Analista';

  @override
  String get monthDetailRouteName => AnalystTradeUnhiredHistoryMonthDetailPage.routeName;

  @override
  Future<AnalystTradeHistory> getTradeMonthHistory({required int id, required DateTime monthDate}) {
    return _analystTradeApi.getAnalystMonthHistory(analystId: id, monthDate: monthDate);
  }

  @override
  String get informationText =>
      'O resultado do mês do analista é somado contando todas as recomendações emitidas no mês de referência. Se não houver saldo para realizar alguma das operações do mês, seu resultado (%) poderá ser maior ou menor do que o resultado (%) do analista. As operações são executadas automaticamente em ordem cronológica.';

  @override
  String get hiringRouteName => AnalystTradeHiringPage.routeName;
}
