import 'package:get/get.dart';

import '../../../api/trade/analyst_trade_api.dart';
import '../../../models/trade/trade_month_performance.dart';
import '../../../pages/trade/analyst/analyst_trade_description_page.dart';
import '../../../pages/trade/analyst/analyst_trade_hiring_page.dart';
import '../../../pages/trade/analyst/analyst_trade_unhired_history_month_page.dart';
import '../trade_unhired_history_page_controller.dart';

class AnalystTradeUnhiredHistoryPageController extends TradeUnhiredHistoryPageController {
  final _analystTradeApi = Get.find<AnalystTradeApi>();

  @override
  String get tradeLabel => 'Analista';

  @override
  String get profileButtonLabel => 'Perfil do Analista';

  @override
  String get profileRouteName => AnalystTradeDescriptionPage.routeName;

  @override
  String get hiringRouteName => AnalystTradeHiringPage.routeName;

  @override
  String get monthHistoryRouteName => AnalystTradeUnhiredHistoryMonthPage.routeName;

  @override
  Future<List<TradeMonthPerformance>> getTradeLastYearHistory({required int id}) {
    return _analystTradeApi.getAnalystLastYearHistory(analystId: id);
  }
}
