import 'package:get/get.dart';

import '../../../api/trade/analyst_trade_api.dart';
import '../../../models/trade/trade.dart';
import '../../../models/trade/trade_required_balance_calculation.dart';
import '../../../pages/trade/analyst/analyst_trade_description_page.dart';
import '../../../pages/trade/analyst/analyst_trade_hired_history_page.dart';
import '../../../pages/trade/analyst/analyst_trade_list_page.dart';
import '../../../pages/trade/analyst/analyst_trade_my_list_page.dart';
import '../../../pages/trade/analyst/analyst_trade_how_it_works_page.dart';
import '../../../pages/trade/analyst/analyst_trade_minimum_balance_page.dart';
import '../../../pages/trade/analyst/analyst_trade_unhired_history_month_page.dart';
import '../../../pages/trade/analyst/analyst_trade_unhired_history_page.dart';
import 'analyst_trade_list_page_controller.dart';
import '../trade_hiring_page_controller.dart';
import '../trade_history_controller.dart';
import '../trade_list_page_controller.dart';

class AnalystTradeHiringPageController extends TradeHiringPageController {
  final _analystTradeApi = Get.find<AnalystTradeApi>();

  @override
  Future<TradeRequiredBalanceCalculation> getRequiredBalance({required int id, required double amount, required bool isActive}) {
    return _analystTradeApi.getAnalystRequiredBalanceForAmount(analystId: id, amount: amount, isActive: isActive);
  }

  @override
  String get serviceTitle => 'Trade do Analista';

  @override
  Future<void> editTrade({
    required int id,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) {
    return _analystTradeApi.editAnalyst(
      analystId: id,
      amount: amount,
      signature: signature,
      didAcceptTerms: didAcceptTerms,
      isProfileSuitabilityUnfit: isProfileSuitabilityUnfit,
      contractCount: contractCount,
    );
  }

  @override
  Future<void> hireTrade({
    required int id,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) {
    return _analystTradeApi.hireAnalyst(
      analystId: id,
      amount: amount,
      signature: signature,
      didAcceptTerms: didAcceptTerms,
      isProfileSuitabilityUnfit: isProfileSuitabilityUnfit,
      contractCount: contractCount,
    );
  }

  @override
  String get tradeReferenceText => 'pelo analista';

  @override
  Future<void> deactivateTrade({required int id, required List<List<int>> signature}) {
    return _analystTradeApi.deactivateAnalyst(analystId: id, signature: signature);
  }

  @override
  String get purchaseListPageRouteName => AnalystTradeListPage.routeName;

  @override
  String get myInvestmentsListPageRouteName => AnalystTradeMyListPage.routeName;

  @override
  bool get isListPageControllerRegistered => Get.isRegistered<AnalystTradeListPageController>();

  @override
  TradeListPageController get listPageController => Get.find<AnalystTradeListPageController>();

  @override
  String get tradeTile => 'Analista';

  @override
  int get tradeTermsId => 33;

  @override
  int get distributorFeeTextId => 1;

  @override
  void showHistoryPage(Trade trade) {
    TradeHistoryController.tryNavigatingToHistoryPage(
      trade: trade,
      unhiredHistoryRouteName: AnalystTradeUnhiredHistoryPage.routeName,
      unhiredHistoryMontRouteName: AnalystTradeUnhiredHistoryMonthPage.routeName,
      hiredHistoryRouteName: AnalystTradeHiredHistoryPage.routeName,
    );
  }

  @override
  String get howItWorksPageRouteName => AnalystTradeHowItWorksPage.routeName;

  @override
  String get descriptionPageRouteName => AnalystTradeDescriptionPage.routeName;

  @override
  String get minimumBalancePageRouteName => AnalystTradeMinimumBalancePage.routeName;
}
