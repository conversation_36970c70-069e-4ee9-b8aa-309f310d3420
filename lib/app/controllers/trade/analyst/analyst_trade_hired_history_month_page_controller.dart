import 'package:get/get.dart';

import '../../../api/trade/analyst_trade_api.dart';
import '../../../models/trade/trade_history.dart';
import '../../../pages/trade/analyst/analyst_trade_hired_history_closed_position_detail_page.dart';
import '../../../utils/extensions.dart';
import '../../../widgets/info/amount_colored_chip.dart';
import '../../../widgets/info/gain_loss_chip.dart';
import '../trade_hired_history_month_page_controller.dart';

class AnalystTradeHiredHistoryMonthPageController extends TradeHiredHistoryMonthPageController {
  final _analystTradeApi = Get.find<AnalystTradeApi>();

  @override
  String get tradeLabel => 'Analista';

  @override
  String get performanceLabel => 'Gain/Loss';

  @override
  AmountColoredChip get performanceChip => GainLossChip(
        monthHistory?.closedPositionsMonthResultAmount ?? 0,
        isPoints: trade?.isBmf == true,
      );

  @override
  String? get investedLabel => 'Aplicado';

  @override
  String? get investedAmount => monthHistory?.closedPositionsMonthInvestedAmount.toMaskedAmount(contracts: trade?.isBmf == true ? 1 : null);

  @override
  String? get resultLabel => 'Resultado bruto';

  @override
  String? get resultAmount => monthHistory?.closedPositionsMonthResultAmount.toMaskedAmount(showPlusSign: trade?.isBmf == true);

  @override
  String get closedPositionDetailRouteName => AnalystTradeHiredHistoryClosedPositionDetailPage.routeName;

  @override
  String get informationReferenceText => 'pelo analista';

  @override
  Future<TradeHistory> getClosedPositionsOfMonth({required int id, required DateTime monthDate}) {
    return _analystTradeApi.getAnalystClosedPositionsOfMonth(analystId: id, monthDate: monthDate);
  }
}
