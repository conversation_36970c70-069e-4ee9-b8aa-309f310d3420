import 'package:get/get.dart';

import '../../../api/trade/analyst_trade_api.dart';
import '../../../models/trade/trade_history.dart';
import '../../../models/trade/trade_month_performance.dart';
import '../../../pages/trade/analyst/analyst_trade_hired_history_month_page.dart';
import '../../../pages/trade/analyst/analyst_trade_hired_history_open_position_detail_page.dart';
import '../../../pages/trade/analyst/analyst_trade_hiring_page.dart';
import '../../../utils/extensions.dart';
import '../trade_hired_history_page_controller.dart';

class AnalystTradeHiredHistoryPageController extends TradeHiredHistoryPageController {
  final _analystTradeApi = Get.find<AnalystTradeApi>();

  @override
  String get tradeLabel => 'Analista';

  @override
  String get performanceLabel => 'Resultado bruto desde a primeira ativação';

  @override
  AmountMask get performanceMask => trade.isBmf ? AmountMask.points : AmountMask.percentage;

  @override
  String get editingRouteName => AnalystTradeHiringPage.routeName;

  @override
  String get closedPositionsMonthRouteName => AnalystTradeHiredHistoryMonthPage.routeName;

  @override
  String get openPositionDetailRouteName => AnalystTradeHiredHistoryOpenPositionDetailPage.routeName;

  @override
  Future<TradeHistory> getTradeOpenPositions({required int id}) {
    return _analystTradeApi.getAnalystOpenPositions(analystId: id);
  }

  @override
  Future<List<TradeMonthPerformance>> getTradeMonthsWithClosedPositions({required int id}) {
    return _analystTradeApi.getAnalystMonthsWithClosedPositions(analystId: id);
  }

  @override
  String get closedPositionsMonthPerformanceLabel => 'Gain/Loss';

  @override
  AmountMask get closedPositionsMonthPerformanceMask => trade.isBmf ? AmountMask.points : AmountMask.percentage;
}
