import 'package:get/get.dart';

import '../../models/trade/trade.dart';
import '../../models/trade/trade_history.dart';
import '../../models/trade/trade_month_performance.dart';
import 'trade_history_controller.dart';

abstract class TradeUnhiredHistoryMonthPageController extends GetxController {
  var selectedMonth = Get.arguments as TradeMonthPerformance;
  TradeHistory? monthHistory;
  Trade? get trade => Get.isRegistered<TradeHistoryController>() ? Get.find<TradeHistoryController>().trade : null;

  String get tradeLabel;
  Future<TradeHistory> getTradeMonthHistory({required int id, required DateTime monthDate});
  String get monthDetailRouteName;
  String get informationText;
  String get hiringRouteName;

  Future<void> loadMonthHistory() async {
    monthHistory = await getTradeMonthHistory(id: trade?.id ?? 0, monthDate: selectedMonth.monthDate);
    update();
  }
}
