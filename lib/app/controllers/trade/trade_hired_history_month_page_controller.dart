import 'package:get/get.dart';

import '../../models/trade/trade.dart';
import '../../models/trade/trade_history.dart';
import '../../models/trade/trade_month_performance.dart';
import '../../widgets/info/amount_colored_chip.dart';
import 'trade_history_controller.dart';

abstract class TradeHiredHistoryMonthPageController extends GetxController {
  var selectedMonth = Get.arguments as TradeMonthPerformance;
  TradeHistory? monthHistory;
  Trade? get trade => Get.isRegistered<TradeHistoryController>() ? Get.find<TradeHistoryController>().trade : null;

  String get tradeLabel;
  String get performanceLabel;
  AmountColoredChip get performanceChip;
  String? get investedLabel;
  String? get investedAmount;
  String? get resultLabel;
  String? get resultAmount;
  Future<TradeHistory> getClosedPositionsOfMonth({required int id, required DateTime monthDate});
  String get closedPositionDetailRouteName;
  String get informationReferenceText;

  Future<void> loadMonthHistory() async {
    monthHistory = await getClosedPositionsOfMonth(id: trade?.id ?? 0, monthDate: selectedMonth.monthDate);
    update();
  }
}
