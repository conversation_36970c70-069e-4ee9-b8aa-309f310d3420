import 'package:get/get.dart';

import '../../../api/trade/partner_trade_api.dart';
import '../../../models/trade/trade_history.dart';
import '../../../pages/trade/partner/partner_trade_hiring_page.dart';
import '../../../pages/trade/partner/partner_trade_unhired_history_month_detail_page.dart';
import '../trade_unhired_history_month_page_controller.dart';

class PartnerTradeUnhiredHistoryMonthPageController extends TradeUnhiredHistoryMonthPageController {
  final _partnerTradeApi = Get.find<PartnerTradeApi>();

  @override
  String get tradeLabel => 'Parceiro';

  @override
  String get monthDetailRouteName => PartnerTradeUnhiredHistoryMonthDetailPage.routeName;

  @override
  Future<TradeHistory> getTradeMonthHistory({required int id, required DateTime monthDate}) {
    return _partnerTradeApi.getPartnerMonthHistory(partnerId: id, monthDate: monthDate);
  }

  @override
  String get informationText =>
      'O resultado do mês do parceiro é somado contando todas as recomendações emitidas no mês de referência. Se não houver saldo para realizar alguma das operações do mês, seu resultado (%) poderá ser maior ou menor do que o resultado (%) do parceiro. As operações são executadas automaticamente em ordem cronológica.';

  @override
  String get hiringRouteName => PartnerTradeHiringPage.routeName;
}
