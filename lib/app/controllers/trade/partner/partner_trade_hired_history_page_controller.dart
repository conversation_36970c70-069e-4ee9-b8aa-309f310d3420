import 'package:get/get.dart';

import '../../../api/trade/partner_trade_api.dart';
import '../../../models/trade/trade_history.dart';
import '../../../models/trade/trade_month_performance.dart';
import '../../../pages/trade/partner/partner_trade_hired_history_month_page.dart';
import '../../../pages/trade/partner/partner_trade_hired_history_open_position_detail_page.dart';
import '../../../pages/trade/partner/partner_trade_hiring_page.dart';
import '../../../utils/extensions.dart';
import '../trade_hired_history_page_controller.dart';

class PartnerTradeHiredHistoryPageController extends TradeHiredHistoryPageController {
  final _partnerTradeApi = Get.find<PartnerTradeApi>();

  @override
  String get tradeLabel => 'Parceiro';

  @override
  String get performanceLabel => trade.isStructuredOperation ? 'Resultado líquido desde a primeira ativação' : 'Resultado bruto desde a primeira ativação';

  @override
  AmountMask get performanceMask => trade.isStructuredOperation
      ? AmountMask.currency
      : trade.isBmf
          ? AmountMask.points
          : AmountMask.percentage;

  @override
  String get editingRouteName => PartnerTradeHiringPage.routeName;

  @override
  String get closedPositionsMonthRouteName => PartnerTradeHiredHistoryMonthPage.routeName;

  @override
  String get openPositionDetailRouteName => PartnerTradeHiredHistoryOpenPositionDetailPage.routeName;

  @override
  Future<TradeHistory> getTradeOpenPositions({required int id}) {
    return _partnerTradeApi.getPartnerOpenPositions(partnerId: id);
  }

  @override
  Future<List<TradeMonthPerformance>> getTradeMonthsWithClosedPositions({required int id}) {
    return _partnerTradeApi.getPartnerMonthsWithClosedPositions(partnerId: id);
  }

  @override
  String get closedPositionsMonthPerformanceLabel => 'Resultado (mês)';

  @override
  AmountMask get closedPositionsMonthPerformanceMask => AmountMask.currency;
}
