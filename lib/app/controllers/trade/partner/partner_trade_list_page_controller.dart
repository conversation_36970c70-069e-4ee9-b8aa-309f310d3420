import 'package:get/get.dart';

import '../../../api/trade/partner_trade_api.dart';
import '../../../models/trade/trade.dart';
import '../../../pages/trade/partner/partner_trade_description_page.dart';
import '../../../pages/trade/partner/partner_trade_hired_history_page.dart';
import '../../../pages/trade/partner/partner_trade_hiring_page.dart';
import '../../../pages/trade/partner/partner_trade_how_it_works_page.dart';
import '../../../pages/trade/partner/partner_trade_unhired_history_month_page.dart';
import '../../../pages/trade/partner/partner_trade_unhired_history_page.dart';
import '../../../utils/extensions.dart';
import '../trade_hiring_controller.dart';
import '../trade_history_controller.dart';
import '../trade_list_page_controller.dart';

class PartnerTradeListPageController extends TradeListPageController {
  static const noHistoryRecommendation = ' Por enquanto confira o perfil do parceiro para saber mais.';

  final _partnerTradeApi = Get.find<PartnerTradeApi>();

  @override
  String get pageTitle => 'Trade do Parceiro';

  @override
  String get availableTradesTabText => 'Parceiros disponíveis';

  @override
  String get hiredTradesTabText => 'Parceiros contratados';

  @override
  void showInfoPage() {
    Get.toNamed(PartnerTradeHowItWorksPage.routeName);
  }

  @override
  void showProfilePage(Trade trade) => Get.toNamed(PartnerTradeDescriptionPage.routeName, arguments: trade);

  @override
  void showHistoryPage(Trade trade) {
    TradeHistoryController.tryNavigatingToHistoryPage(
      trade: trade,
      unhiredHistoryRouteName: PartnerTradeUnhiredHistoryPage.routeName,
      unhiredHistoryMontRouteName: PartnerTradeUnhiredHistoryMonthPage.routeName,
      hiredHistoryRouteName: PartnerTradeHiredHistoryPage.routeName,
      noHistoryRecommendation: noHistoryRecommendation,
    );
  }

  @override
  void showHiringPage(Trade trade) {
    TradeHiringController.tryNavigatingToHiringPage(
      trade: trade,
      routeName: PartnerTradeHiringPage.routeName,
    );
  }

  @override
  Future<(List<Trade>, List<Trade>)> getTrades() async {
    final response = await _partnerTradeApi.getPartners();
    return (
      response.availablePartners.sortedByPerformance.alteranatedByMarket,
      response.hiredPartners.sortedByPerformance.alteranatedByMarket,
    );
  }

  @override
  bool get isShowMoreButtonVisible => false;

  @override
  List<Trade> get relevantAvailableTrades => availableTrades;

  @override
  String get deactivatedText => 'O Parceiro não está mais disponível';

  @override
  String getResultAmountMaskedText(Trade trade) {
    if (trade.isHired) {
      return trade.performance.toMaskedAmount(mask: AmountMask.currency);
    } else {
      return trade.performance.toMaskedAmount(
        mask: trade.isBmf ? AmountMask.points : AmountMask.percentage,
        showPlusSign: true,
      );
    }
  }

  @override
  String getResultSubtitle(Trade trade) => trade.isHired ? 'Resultado líquido' : 'Resultado bruto';

  @override
  String getResultTooltipText(Trade trade) => trade.isHired
      ? 'Resultado da operação descontado a corretagem'
          '\n(não são considerados taxas e emolumentos da'
          '\nbolsa nem eventuais taxas de aluguel)'
      : 'Resultado bruto dos últimos 12 meses'
          '\nou desde o início das recomendações,'
          '\nconsiderando o valor mínimo aplicável.';
}
