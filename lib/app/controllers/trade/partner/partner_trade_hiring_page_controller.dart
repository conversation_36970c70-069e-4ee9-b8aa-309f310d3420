import 'package:get/get.dart';

import '../../../api/trade/partner_trade_api.dart';
import '../../../models/trade/trade.dart';
import '../../../models/trade/trade_required_balance_calculation.dart';
import '../../../pages/trade/partner/partner_trade_description_page.dart';
import '../../../pages/trade/partner/partner_trade_hired_history_page.dart';
import '../../../pages/trade/partner/partner_trade_list_page.dart';
import '../../../pages/trade/partner/partner_trade_my_list_page.dart';
import '../../../pages/trade/partner/partner_trade_how_it_works_page.dart';
import '../../../pages/trade/partner/partner_trade_minimum_balance_page.dart';
import '../../../pages/trade/partner/partner_trade_unhired_history_month_page.dart';
import '../../../pages/trade/partner/partner_trade_unhired_history_page.dart';
import 'partner_trade_list_page_controller.dart';
import '../trade_hiring_page_controller.dart';
import '../trade_history_controller.dart';
import '../trade_list_page_controller.dart';

class PartnerTradeHiringPageController extends TradeHiringPageController {
  final _partnerTradeApi = Get.find<PartnerTradeApi>();

  @override
  Future<TradeRequiredBalanceCalculation> getRequiredBalance({required int id, required double amount, required bool isActive}) {
    return _partnerTradeApi.getPartnerRequiredBalanceForAmount(partnerId: id, amount: amount, isActive: isActive);
  }

  @override
  String get serviceTitle => 'Trade do Parceiro';

  @override
  Future<void> editTrade({
    required int id,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) {
    return _partnerTradeApi.editPartner(
      partnerId: id,
      amount: amount,
      signature: signature,
      didAcceptTerms: didAcceptTerms,
      isProfileSuitabilityUnfit: isProfileSuitabilityUnfit,
      contractCount: contractCount,
    );
  }

  @override
  Future<void> hireTrade({
    required int id,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) {
    return _partnerTradeApi.hirePartner(
      partnerId: id,
      amount: amount,
      signature: signature,
      didAcceptTerms: didAcceptTerms,
      isProfileSuitabilityUnfit: isProfileSuitabilityUnfit,
      contractCount: contractCount,
    );
  }

  @override
  String get tradeReferenceText => 'pelo parceiro';

  @override
  Future<void> deactivateTrade({required int id, required List<List<int>> signature}) {
    return _partnerTradeApi.deactivatePartner(partnerId: id, signature: signature);
  }

  @override
  String get purchaseListPageRouteName => PartnerTradeListPage.routeName;

  @override
  String get myInvestmentsListPageRouteName => PartnerTradeMyListPage.routeName;

  @override
  bool get isListPageControllerRegistered => Get.isRegistered<PartnerTradeListPageController>();

  @override
  TradeListPageController get listPageController => Get.find<PartnerTradeListPageController>();

  @override
  String get tradeTile => 'Parceiro';

  @override
  int get tradeTermsId => 112;

  @override
  int get distributorFeeTextId => 29;

  @override
  void showHistoryPage(Trade trade) {
    TradeHistoryController.tryNavigatingToHistoryPage(
      trade: trade,
      unhiredHistoryRouteName: PartnerTradeUnhiredHistoryPage.routeName,
      unhiredHistoryMontRouteName: PartnerTradeUnhiredHistoryMonthPage.routeName,
      hiredHistoryRouteName: PartnerTradeHiredHistoryPage.routeName,
      noHistoryRecommendation: PartnerTradeListPageController.noHistoryRecommendation,
    );
  }

  @override
  String get howItWorksPageRouteName => PartnerTradeHowItWorksPage.routeName;

  @override
  String get descriptionPageRouteName => PartnerTradeDescriptionPage.routeName;

  @override
  String get minimumBalancePageRouteName => PartnerTradeMinimumBalancePage.routeName;
}
