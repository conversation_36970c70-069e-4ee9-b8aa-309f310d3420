import 'package:get/get.dart';

import '../../../api/trade/partner_trade_api.dart';
import '../../../models/trade/trade_month_performance.dart';
import '../../../pages/trade/partner/partner_trade_description_page.dart';
import '../../../pages/trade/partner/partner_trade_hiring_page.dart';
import '../../../pages/trade/partner/partner_trade_unhired_history_month_page.dart';
import '../trade_unhired_history_page_controller.dart';

class PartnerTradeUnhiredHistoryPageController extends TradeUnhiredHistoryPageController {
  final _partnerTradeApi = Get.find<PartnerTradeApi>();

  @override
  String get tradeLabel => 'Parceiro';

  @override
  String get profileButtonLabel => 'Perfil do Parceiro';

  @override
  String get profileRouteName => PartnerTradeDescriptionPage.routeName;

  @override
  String get hiringRouteName => PartnerTradeHiringPage.routeName;

  @override
  String get monthHistoryRouteName => PartnerTradeUnhiredHistoryMonthPage.routeName;

  @override
  Future<List<TradeMonthPerformance>> getTradeLastYearHistory({required int id}) {
    return _partnerTradeApi.getPartnerLastYearHistory(partnerId: id);
  }
}
