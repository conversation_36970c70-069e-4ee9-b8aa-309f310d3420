import 'package:get/get.dart';

import '../../../api/trade/partner_trade_api.dart';
import '../../../models/trade/trade_history.dart';
import '../../../pages/trade/partner/partner_trade_hired_history_closed_position_detail_page.dart';
import '../../../widgets/info/amount_colored_chip.dart';
import '../trade_hired_history_month_page_controller.dart';

class PartnerTradeHiredHistoryMonthPageController extends TradeHiredHistoryMonthPageController {
  final _partnerTradeApi = Get.find<PartnerTradeApi>();

  @override
  String get tradeLabel => 'Parceiro';

  @override
  String get performanceLabel => 'Resultado (mês)';

  @override
  AmountColoredChip get performanceChip => AmountColoredChip(monthHistory?.closedPositionsMonthResultAmount ?? 0, showPlusSign: true);

  @override
  String? get investedLabel => null;

  @override
  String? get investedAmount => null;

  @override
  String? get resultLabel => null;

  @override
  String? get resultAmount => null;

  @override
  String get closedPositionDetailRouteName => PartnerTradeHiredHistoryClosedPositionDetailPage.routeName;

  @override
  String get informationReferenceText => 'pelo parceiro';

  @override
  Future<TradeHistory> getClosedPositionsOfMonth({required int id, required DateTime monthDate}) {
    return _partnerTradeApi.getPartnerClosedPositionsOfMonth(partnerId: id, monthDate: monthDate);
  }
}
