import 'package:get/get.dart';

import '../../models/trade/trade_month_performance.dart';
import 'trade_history_controller.dart';

abstract class TradeUnhiredHistoryPageController extends TradeHistoryController {
  final tradeLastYearHistory = <TradeMonthPerformance>[];

  String get tradeLabel;
  String get profileButtonLabel;
  String get profileRouteName;
  String get hiringRouteName;
  String get monthHistoryRouteName;
  Future<List<TradeMonthPerformance>> getTradeLastYearHistory({required int id});

  Future<void> getLastYearHistory() async {
    final months = await getTradeLastYearHistory(id: trade.id ?? 0);
    tradeLastYearHistory.assignAll(months);
    update();
  }

  void onMonthPressed(TradeMonthPerformance selectedMonth) {
    Get.toNamed(monthHistoryRouteName, arguments: selectedMonth);
  }
}
