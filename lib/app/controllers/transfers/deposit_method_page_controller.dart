import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class DepositMethodPageController extends GetxController with GetSingleTickerProviderStateMixin {
  late final tabController = TabController(length: 2, vsync: this, initialIndex: Get.arguments ?? 0);

  String pixVideoCode = 'ujsb1_ihESM';
  String tedVideoCode = 'lnsFh7j67dc';
  String get _videoToload => tabController.index == 1 ? tedVideoCode : pixVideoCode;
  YoutubePlayerController get youtubeController => YoutubePlayerController(
        initialVideoId: _videoToload,
        flags: YoutubePlayerFlags(autoPlay: false, startAt: tabController.index == 1 ? 150 : 0),
      );

  @override
  void onInit() {
    super.onInit();
    tabController.addListener(update);
  }
}
