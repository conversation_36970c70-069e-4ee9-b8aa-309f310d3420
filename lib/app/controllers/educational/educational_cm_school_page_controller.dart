import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../models/youtube_video.dart';

class EducationalCmSchoolPageController extends GetxController {
  // final _educationalApi = Get.find<EducationalApi>();
  // final _extratoApi = Get.find<ExtratoApi>();

  // final bodyKey = GlobalKey<CMBodyState>();
  // final _availableBalance = Rx<SaldosProjecoes?>(null);
  // final courses = <Course>[].obs;

  // Future<List<Course>> fetchBalanceAndCourses() async {
  //   _availableBalance.value = await _extratoApi.saldosProjecoes();
  //   final loadedCourses = await _educationalApi.listCourses();
  //   courses.assignAll(loadedCourses);
  //   return loadedCourses;
  // }

  // Future<void> enroll(Course course) async {
  //   // Verifica saldo disponível
  //   final hasAvailableBalance = _checkBalanceToEnroll(course);
  //   if (!hasAvailableBalance) return;

  //   try {
  //     // Obtém assinatura
  //     final signature = await _getSignature(course);
  //     if (signature == null) return;

  //     // Faz matrícula
  //     await _educationalApi.enroll(id: course.id, signature: signature);

  //     // Atualiza lista de cursos
  //     bodyKey.currentState?.onChanged();

  //     // Mostra mensagem de sucesso
  //     await CMBottomSheet.simple(
  //       topWidget: Padding(
  //         padding: const EdgeInsets.only(bottom: 10),
  //         child: Image.asset('assets/icons/dialog/enrollment.png'),
  //       ),
  //       title: 'Sucesso!',
  //       description: 'Sua matrícula foi realizada\n\nEnviaremos um e-mail com as informações sobre o acesso ao curso.',
  //     ).show();
  //   } catch (error) {
  //     onError(error);
  //   }
  // }

  // bool _checkBalanceToEnroll(Course course) {
  //   if ((_availableBalance.value?.saque ?? 0) < (course.price ?? 0)) return true;
  //   CMBottomSheet.simple(
  //     topWidget: Image.asset('assets/icons/dialog/investment_failed.png'),
  //     title: 'Aviso',
  //     description: 'Você não possui saldo suficiente para fazer a matrícula.',
  //   ).show();
  //   return false;
  // }

  // Future<List<List<int>>?> _getSignature(Course course) async {
  //   final signature = await Get.toNamed(
  //     ElectronicSignaturePage.routeName,
  //     arguments: {
  //       'title': 'Fazer matrícula',
  //       'data': ProductSummary(
  //         fields: [
  //           VerticalField("Curso", course.name, widthFactor: 1),
  //           VerticalField("Descrição", course.longDescription, widthFactor: 1),
  //           const VerticalField("Mensalidade", "Grátis", widthFactor: 1),
  //         ],
  //       ),
  //     },
  //   );
  //   FocusManager.instance.primaryFocus?.unfocus();
  //   if (signature is! List<List<int>>) return null;
  //   return signature;
  // }

  // void goToCourse() => openUrl('https://cmschooltrader.cmcapital.com.br/', useExternalApplicationForIos: true);

  // Lista de vídeos de playlist específica será mostrada temporariamente. Por isso código anterior foi comentado.
  final videos = <YoutubeVideo>[].obs;
  Future<List<YoutubeVideo>> fetchVideos() async {
    final json = jsonDecode(await rootBundle.loadString('assets/json/youtube_playlist_mini.json'));
    final videoJsonList = json['videos'] as List<dynamic>;
    final videoList = <YoutubeVideo>[];
    for (var videoJson in videoJsonList) {
      final video = YoutubeVideo.fromJson(videoJson);
      videoList.add(video);
    }
    videos.assignAll(videoList);
    return videos;
  }
}
