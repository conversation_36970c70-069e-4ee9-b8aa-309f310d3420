import 'package:get/get.dart';

import '../../api/educational/educational_api.dart';
import '../../models/youtube_video.dart';

class EducationalTradersCoachPageController extends GetxController {
  final _educationalApi = Get.find<EducationalApi>();

  final showCardBody = false.obs;
  final expandableCardVideo = Rx<YoutubeVideo?>(null);
  final _allVideos = <YoutubeVideo>[].obs;
  final availableYears = <int>{}.obs;
  final selectedYear = Rx<int?>(null);

  List<YoutubeVideo> get selectedYearVideos => _allVideos.where((video) => video.date != null && video.date!.year == selectedYear.value).toList();

  Future<List<YoutubeVideo>> fetchVideos() async {
    // Obtém vídeos do back
    final loadedVideos = await _educationalApi.listTradersCoachVideos();
    if (loadedVideos.isEmpty) return [];

    // Ordena os vídeos por data decrescente
    loadedVideos.sort((a, b) => a.date != null && b.date != null ? b.date!.compareTo(a.date!) : 0);

    // Obtém anos dos vídeos
    for (var video in loadedVideos) {
      if (video.date == null) continue;
      availableYears.add(video.date!.year);
    }

    // Define vídeo do card expansível
    expandableCardVideo.value = loadedVideos.last;

    // Define ano selecionado
    if (availableYears.isNotEmpty) selectedYear.value = availableYears.first;

    // Atualiza lista geral de vídeos
    _allVideos.assignAll(loadedVideos);

    // Retorna lista de vídeos
    return loadedVideos;
  }
}
