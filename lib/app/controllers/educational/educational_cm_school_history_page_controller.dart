import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../api/educational/educational_api.dart';
import '../../models/course.dart';
import '../../models/period_request.dart';
import '../../pages/tabs/filter_by_period_page.dart';
import '../../widgets/body/cm_body.dart';

class EducationalCmSchoolHistoryPageController extends GetxController {
  final _educationalApi = Get.find<EducationalApi>();

  final bodyKey = GlobalKey<CMBodyState>();
  final courses = <Course>[].obs;
  final dateFilters = FilterByPeriodPage.getFilterDates(DateFilters()).obs;

  Future<List<Course>> fetchHistory() async {
    final loadedCourses = await _educationalApi.listEnrollmentHistory(
      PeriodRequest(
        startDate: dateFilters.value.dataInicio,
        endDate: dateFilters.value.dataFim,
      ),
    );
    courses.assignAll(loadedCourses);
    return courses;
  }

  Future<void> filterByPeriod() async {
    final filters = await Get.toNamed(FilterByPeriodPage.routeName, arguments: dateFilters.value);
    if (filters is! DateFilters && filters.dataInicio == null && filters.dataFim == null) return;
    _updateFilters(filters);
  }

  void _updateFilters(DateFilters filters) {
    dateFilters.value = filters;
    bodyKey.currentState?.onChanged();
  }
}
