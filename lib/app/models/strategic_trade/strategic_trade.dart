import 'package:json_annotation/json_annotation.dart';

part 'strategic_trade.g.dart';

@JsonSerializable(createToJson: true)
class StrategicTrade {
  @Json<PERSON>ey(name: 'operacaoid')
  int? id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'titulo')
  String? title;
  @J<PERSON><PERSON><PERSON>(name: 'descricao')
  String? description;
  @Json<PERSON>ey(name: 'validade')
  DateTime? dueDate;
  @JsonKey(name: 'cancelamento')
  DateTime? cancellation;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'publicacao')
  DateTime? publication;
  @Json<PERSON>ey(name: 'estrutura')
  String? structure;
  @JsonKey(name: 'retornomax')
  double? maximumReturn;
  @Json<PERSON>ey(name: 'retornomaxperc')
  double? maximumReturnPercentage;
  @JsonKey(name: 'riscomax')
  double? maximumRiskPercentage;
  @<PERSON>sonKey(name: 'investimentomin')
  double? minimumInvestment;
  @JsonKey(name: 'spread')
  double? spread;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'documento')
  Uri? document;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'tese')
  Uri? tese;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'mercado')
  String? mercado;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'status')
  String? status;
  @JsonKey(name: 'vies')
  String? vies;

   factory StrategicTrade.fromJson(Map<String, dynamic> json) => _$StrategicTradeFromJson(json);
  Map<String, dynamic> toJson() => _$StrategicTradeToJson(this);

  StrategicTrade({
    this.id,
    this.title,
    this.description,
    this.dueDate,
    this.cancellation,
    this.publication,
    this.structure,
    this.maximumReturn,
    this.maximumReturnPercentage,
    this.maximumRiskPercentage,
    this.minimumInvestment,
    this.spread,
    this.document,
    this.tese,
    this.mercado,
    this.status,
    this.vies,
  });
}
