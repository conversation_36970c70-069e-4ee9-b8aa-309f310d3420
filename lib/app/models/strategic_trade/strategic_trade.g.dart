// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'strategic_trade.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StrategicTrade _$StrategicTradeFromJson(Map<String, dynamic> json) =>
    StrategicTrade(
      id: (json['operacaoid'] as num?)?.toInt(),
      title: json['titulo'] as String?,
      description: json['descricao'] as String?,
      dueDate: json['validade'] == null
          ? null
          : DateTime.parse(json['validade'] as String),
      cancellation: json['cancelamento'] == null
          ? null
          : DateTime.parse(json['cancelamento'] as String),
      publication: json['publicacao'] == null
          ? null
          : DateTime.parse(json['publicacao'] as String),
      structure: json['estrutura'] as String?,
      maximumReturn: (json['retornomax'] as num?)?.toDouble(),
      maximumReturnPercentage: (json['retornomaxperc'] as num?)?.toDouble(),
      maximumRiskPercentage: (json['riscomax'] as num?)?.toDouble(),
      minimumInvestment: (json['investimentomin'] as num?)?.toDouble(),
      spread: (json['spread'] as num?)?.toDouble(),
      document: json['documento'] == null
          ? null
          : Uri.parse(json['documento'] as String),
      tese: json['tese'] == null ? null : Uri.parse(json['tese'] as String),
      mercado: json['mercado'] as String?,
      status: json['status'] as String?,
      vies: json['vies'] as String?,
    );

Map<String, dynamic> _$StrategicTradeToJson(StrategicTrade instance) =>
    <String, dynamic>{
      'operacaoid': instance.id,
      'titulo': instance.title,
      'descricao': instance.description,
      'validade': instance.dueDate?.toIso8601String(),
      'cancelamento': instance.cancellation?.toIso8601String(),
      'publicacao': instance.publication?.toIso8601String(),
      'estrutura': instance.structure,
      'retornomax': instance.maximumReturn,
      'retornomaxperc': instance.maximumReturnPercentage,
      'riscomax': instance.maximumRiskPercentage,
      'investimentomin': instance.minimumInvestment,
      'spread': instance.spread,
      'documento': instance.document?.toString(),
      'tese': instance.tese?.toString(),
      'mercado': instance.mercado,
      'status': instance.status,
      'vies': instance.vies,
    };
