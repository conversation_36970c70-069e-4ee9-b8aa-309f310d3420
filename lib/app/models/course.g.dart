// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Course _$CourseFromJson(Map<String, dynamic> json) => Course(
      id: (json['esc_cursos_id'] as num).toInt(),
      code: json['esc_cursos_cod_integracao'] as String?,
      name: json['esc_cursos_nome'] as String?,
      price: (json['esc_cursos_valor'] as num?)?.toDouble(),
      shortDescription: json['esc_cursos_descricao_curta'] as String?,
      longDescription: json['esc_cursos_descricao_longa'] as String?,
      imageUrl: json['link_img'] as String?,
      isEnrolled: json['matriculado'] as bool? ?? false,
      startDate: json['esc_cursos_usuarios_dt_inicio'] == null
          ? null
          : DateTime.parse(json['esc_cursos_usuarios_dt_inicio'] as String),
      endDate: json['esc_cursos_usuarios_dt_fim'] == null
          ? null
          : DateTime.parse(json['esc_cursos_usuarios_dt_fim'] as String),
    );

Map<String, dynamic> _$CourseToJson(Course instance) => <String, dynamic>{
      'esc_cursos_id': instance.id,
      'esc_cursos_cod_integracao': instance.code,
      'esc_cursos_nome': instance.name,
      'esc_cursos_valor': instance.price,
      'esc_cursos_descricao_curta': instance.shortDescription,
      'esc_cursos_descricao_longa': instance.longDescription,
      'link_img': instance.imageUrl,
      'matriculado': instance.isEnrolled,
      'esc_cursos_usuarios_dt_inicio': instance.startDate?.toIso8601String(),
      'esc_cursos_usuarios_dt_fim': instance.endDate?.toIso8601String(),
    };
