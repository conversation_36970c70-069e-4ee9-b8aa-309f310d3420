import 'package:json_annotation/json_annotation.dart';

part 'course.g.dart';

@JsonSerializable()
class Course {
  @Json<PERSON><PERSON>(name: 'esc_cursos_id')
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'esc_cursos_cod_integracao')
  final String? code;
  @J<PERSON><PERSON><PERSON>(name: 'esc_cursos_nome')
  final String? name;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'esc_cursos_valor')
  final double? price;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'esc_cursos_descricao_curta')
  final String? shortDescription;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'esc_cursos_descricao_longa')
  final String? longDescription;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'link_img')
  final String? imageUrl;
  @J<PERSON><PERSON><PERSON>(name: 'matriculado')
  final bool isEnrolled;
  @<PERSON>son<PERSON>ey(name: 'esc_cursos_usuarios_dt_inicio')
  final DateTime? startDate;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'esc_cursos_usuarios_dt_fim')
  final DateTime? endDate;

  Course({
    required this.id,
    this.code,
    this.name,
    this.price,
    this.shortDescription,
    this.longDescription,
    this.imageUrl,
    this.isEnrolled = false,
    this.startDate,
    this.endDate,
  });

  factory Course.fromJson(Map<String, dynamic> json) => _$CourseFromJson(json);
  Map<String, dynamic> toJson() => _$CourseToJson(this);
}
