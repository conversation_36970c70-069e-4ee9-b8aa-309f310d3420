import 'package:json_annotation/json_annotation.dart';

part 'trade_required_balance_calculation.g.dart';

@JsonSerializable(createToJson: false)
class TradeRequiredBalanceCalculation {
  @Json<PERSON>ey(name: 'valorgarantia')
  final double? requiredBalance;
  @Json<PERSON>ey(name: 'multiplicador')
  final int? equationMultiplier;

  TradeRequiredBalanceCalculation({
    this.requiredBalance,
    this.equationMultiplier,
  });

  factory TradeRequiredBalanceCalculation.fromJson(Map<String, dynamic> json) => _$TradeRequiredBalanceCalculationFromJson(json);
}
