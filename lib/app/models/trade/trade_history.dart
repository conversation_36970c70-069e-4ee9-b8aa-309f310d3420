import 'package:json_annotation/json_annotation.dart';

import 'trade_position.dart';

part 'trade_history.g.dart';

@JsonSerializable(createToJson: false)
class TradeHistory {
  // Recomendações
  @Json<PERSON>ey(name: 'historico')
  final List<TradePosition>? recommendationsSpotFutures;
  @Json<PERSON>ey(name: 'historicoestruturadas')
  final List<TradePosition>? recommendationsStructured;
  @JsonKey(name: 'resultadomes')
  double? recommendationsMonthResult;
  @Json<PERSON>ey(name: 'resultadoultimos12meses')
  double? recommendationsLast12MonthsResult;
  @JsonKey(name: 'rendimento')
  double? recommendationsYearPerformancePercentage;

  // Operação em aberto ou encerrada
  @Json<PERSON>ey(name: 'operacoesestruturadas')
  final List<TradePosition>? positionsStructured;

  // Operações em aberto
  @JsonKey(name: 'operacoesabertas')
  final List<TradePosition>? openPositionsSpotFutures;

  // Operações encerradas
  @JsonKey(name: 'operacoesencerradas')
  final List<TradePosition>? closedPositionsSpotFutures;
  @Json<PERSON>ey(name: 'totalvaloraplicado')
  double? closedPositionsMonthInvestedAmount;
  @JsonKey(name: 'totalresultadomesvalor')
  double? closedPositionsMonthResultAmount;
  @JsonKey(name: 'totalresultadomesporcentagem')
  double? closedPositionsMonthResultPercentage;

  TradeHistory({
    this.recommendationsSpotFutures,
    this.recommendationsStructured,
    this.recommendationsMonthResult,
    this.recommendationsYearPerformancePercentage,
    this.positionsStructured,
    this.openPositionsSpotFutures,
    this.closedPositionsSpotFutures,
    this.closedPositionsMonthInvestedAmount,
    this.closedPositionsMonthResultAmount,
    this.closedPositionsMonthResultPercentage,
  });

  factory TradeHistory.fromJson(Map<String, dynamic> json) => _$TradeHistoryFromJson(json);

  List<TradePosition> get recommendations {
    if ((recommendationsStructured ?? []).isNotEmpty) return recommendationsStructured!;
    return recommendationsSpotFutures ?? [];
  }

  List<TradePosition> get openPositions {
    if ((positionsStructured ?? []).isNotEmpty) return positionsStructured!;
    return openPositionsSpotFutures ?? [];
  }

  List<TradePosition> get closedPositions {
    if ((positionsStructured ?? []).isNotEmpty) return positionsStructured!;
    return closedPositionsSpotFutures ?? [];
  }
}
