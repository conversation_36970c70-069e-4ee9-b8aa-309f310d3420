import 'package:json_annotation/json_annotation.dart';

import '../trade.dart';

part 'analyst_trade_list.g.dart';

@JsonSerializable(createToJson: false)
class AnalystTradeList {
  @JsonKey(name: 'analistasdisponiveis')
  final List<Trade> availableAnalysts;

  @JsonKey(name: 'analistascontratados')
  final List<Trade> hiredAnalysts;

  AnalystTradeList({
    this.availableAnalysts = const [],
    this.hiredAnalysts = const [],
  });

  factory AnalystTradeList.fromJson(Map<String, dynamic> json) => _$AnalystTradeListFromJson(json);
}
