import 'package:json_annotation/json_annotation.dart';

import '../trade_history.dart';
import '../trade_position.dart';

part 'analyst_trade_history.g.dart';

@JsonSerializable(createToJson: false)
class AnalystTradeHistory extends TradeHistory {
  @override
  @JsonKey(name: 'historicoanalista')
  final List<TradePosition> recommendationsSpotFutures;

  AnalystTradeHistory({
    super.recommendationsMonthResult,
    super.recommendationsYearPerformancePercentage,
    this.recommendationsSpotFutures = const [],
    super.recommendationsStructured,
  });

  factory AnalystTradeHistory.fromJson(Map<String, dynamic> json) => _$AnalystTradeHistoryFromJson(json);
}
