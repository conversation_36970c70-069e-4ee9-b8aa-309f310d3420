import 'package:json_annotation/json_annotation.dart';

part 'trade_month_performance.g.dart';

@JsonSerializable(createToJson: false)
class TradeMonthPerformance {
  TradeMonthPerformance({
    this.month,
    this.year,
    this.performance,
  });

  @Json<PERSON>ey(name: 'mes')
  final int? month;
  @<PERSON>son<PERSON>ey(name: 'ano')
  final int? year;
  @JsonKey(name: 'rendimento')
  final double? performance;

  DateTime get monthDate => DateTime(year ?? 0, month ?? 0);

  factory TradeMonthPerformance.fromJson(Map<String, dynamic> json) => _$TradeMonthPerformanceFromJson(json);
}
