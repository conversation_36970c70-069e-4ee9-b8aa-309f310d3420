import 'package:json_annotation/json_annotation.dart';

import '../trade.dart';

part 'partner_trade_list.g.dart';

@JsonSerializable(createToJson: false)
class PartnerTradeList {
  @JsonKey(name: 'analistasdisponiveis')
  final List<Trade> availablePartners;

  @JsonKey(name: 'analistascontratados')
  final List<Trade> hiredPartners;

  PartnerTradeList({
    this.availablePartners = const [],
    this.hiredPartners = const [],
  });

  factory PartnerTradeList.fromJson(Map<String, dynamic> json) => _$PartnerTradeListFromJson(json);
}
