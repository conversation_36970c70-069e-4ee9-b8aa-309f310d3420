// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trade_position.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TradePosition _$TradePositionFromJson(Map<String, dynamic> json) =>
    TradePosition(
      id: (json['id'] as num?)?.toDouble(),
      ticker: json['ticker'] as String?,
      positionType:
          TradePositionType.fromId((json['compravenda'] as num?)?.toInt()),
      market: TradeMarketType.fromId((json['tipomercado'] as num?)?.toInt()),
      endDate: json['dataencerramento'] == null
          ? null
          : DateTime.parse(json['dataencerramento'] as String),
      entry: (json['entrada'] as num?)?.toDouble(),
      numberOfContracts: (json['numerodecontratos'] as num?)?.toInt(),
      recommendationStartDate: json['datarecomendacao'] == null
          ? null
          : DateTime.parse(json['datarecomendacao'] as String),
      recommendationFirstDate: json['dataprimeirarecomendacao'] == null
          ? null
          : DateTime.parse(json['dataprimeirarecomendacao'] as String),
      recommendationExitPrice: (json['saida'] as num?)?.toDouble(),
      recommendationResultStatus: json['resultadotipo'] as String?,
      recommendationResultPercentage:
          (json['resultadoporcentagem'] as num?)?.toDouble(),
      recommendationTitle: json['operacao'] as String?,
      initialAmount: (json['valorinicial'] as num?)?.toDouble(),
      finalAmount: (json['valorfinal'] as num?)?.toDouble(),
      recommendationGrossResult: (json['resultadobruto'] as num?)?.toDouble(),
      recommendationLongMovements: (json['compras'] as List<dynamic>?)
          ?.map(
              (e) => TradePositionMovement.fromJson(e as Map<String, dynamic>))
          .toList(),
      recommendationShortMovements: (json['vendas'] as List<dynamic>?)
          ?.map(
              (e) => TradePositionMovement.fromJson(e as Map<String, dynamic>))
          .toList(),
      operationStartDate: json['dataoperacao'] == null
          ? null
          : DateTime.parse(json['dataoperacao'] as String),
      operationInvestedAmount: (json['valoraplicado'] as num?)?.toDouble(),
      operationTitle: json['nomeoperacao'] as String?,
      operationTotalAmount: (json['valortotal'] as num?)?.toDouble(),
      operationGain: (json['gain'] as num?)?.toDouble(),
      operationLoss: (json['loss'] as num?)?.toDouble(),
      operationAveragePrice: (json['precomedio'] as num?)?.toDouble(),
      operationFees: (json['taxas'] as num?)?.toDouble(),
      operationNetResult: (json['resultadoliquido'] as num?)?.toDouble(),
      operationHadEnoughBalance: json['saldosuficiente'] as bool? ?? true,
      operationResult: (json['resultado'] as num?)?.toDouble(),
      operationMovements: (json['movimentos'] as List<dynamic>?)
          ?.map(
              (e) => TradePositionMovement.fromJson(e as Map<String, dynamic>))
          .toList(),
      closedPositionWasClosedEarly: json['saidaantecipada'] as bool? ?? false,
      closedPositionResultPercentage: (json['gainloss'] as num?)?.toDouble(),
    );

TradePositionMovement _$TradePositionMovementFromJson(
        Map<String, dynamic> json) =>
    TradePositionMovement(
      movementType: json['tipomovimento'] as String?,
      title: json['titulo'] as String?,
      quantity: (json['quantidade'] as num?)?.toInt(),
      averagePrice: (json['precomedio'] as num?)?.toDouble(),
      financialValue: (json['valorfinanceiro'] as num?)?.toDouble(),
      initialValue: (json['valorinicial'] as num?)?.toDouble(),
    );
