import 'package:collection/collection.dart';
import 'package:json_annotation/json_annotation.dart';

import 'trade_market_type.dart';

part 'trade_position.g.dart';

enum TradePositionType {
  long(1, 'Compra'),
  short(2, 'Venda');

  final int id;
  final String description;

  const TradePositionType(this.id, this.description);

  static TradePositionType? fromId(int? id) => TradePositionType.values.firstWhereOrNull((operation) => operation.id == id);
}

@JsonSerializable(createToJson: false)
class TradePosition {
  final double? id;
  final String? ticker;
  @Json<PERSON><PERSON>(name: 'compravenda', fromJson: TradePositionType.fromId)
  final TradePositionType? positionType;
  @Json<PERSON>ey(name: 'tipomercado', fromJson: TradeMarketType.fromId)
  final TradeMarketType? market;

  @JsonKey(name: 'dataoperacao')
  DateTime? operationStartDate;
  @<PERSON>son<PERSON>ey(name: 'dataencerramento')
  DateTime? endDate;
  @Json<PERSON>ey(name: 'entrada')
  double? entry;
  @Json<PERSON>ey(name: 'numerodecontratos')
  int? numberOfContracts;
  @Json<PERSON>ey(name: 'valorinicial')
  double? initialAmount;
  @JsonKey(name: 'valorfinal')
  double? finalAmount;

  // Recomendação
  @JsonKey(name: 'datarecomendacao')
  DateTime? recommendationStartDate;
  @JsonKey(name: 'dataprimeirarecomendacao')
  DateTime? recommendationFirstDate;
  @JsonKey(name: 'saida')
  double? recommendationExitPrice;
  @JsonKey(name: 'resultadotipo')
  String? recommendationResultStatus;
  @JsonKey(name: 'resultadoporcentagem')
  double? recommendationResultPercentage;
  @JsonKey(name: 'operacao')
  String? recommendationTitle;
  @JsonKey(name: 'resultadobruto')
  double? recommendationGrossResult;
  @JsonKey(name: 'compras')
  List<TradePositionMovement>? recommendationLongMovements;
  @JsonKey(name: 'vendas')
  List<TradePositionMovement>? recommendationShortMovements;

  // Operação em aberto ou encerrada
  @JsonKey(name: 'valoraplicado')
  double? operationInvestedAmount;
  @JsonKey(name: 'nomeoperacao')
  String? operationTitle;
  @JsonKey(name: 'valortotal')
  double? operationTotalAmount;
  @JsonKey(name: 'gain')
  double? operationGain;
  @JsonKey(name: 'loss')
  double? operationLoss;
  @JsonKey(name: 'precomedio')
  double? operationAveragePrice;
  @JsonKey(name: 'taxas')
  double? operationFees;
  @JsonKey(name: 'resultadoliquido')
  double? operationNetResult;
  @JsonKey(name: 'saldosuficiente')
  bool operationHadEnoughBalance;
  @JsonKey(name: 'resultado')
  double? operationResult;
  @JsonKey(name: 'movimentos')
  List<TradePositionMovement>? operationMovements;

  // Operação encerrada
  @JsonKey(name: 'saidaantecipada')
  bool closedPositionWasClosedEarly;
  @JsonKey(name: 'gainloss')
  double? closedPositionResultPercentage;

  TradePosition({
    required this.id,
    required this.ticker,
    required this.positionType,
    this.market,
    this.endDate,
    this.entry,
    this.numberOfContracts,
    this.recommendationStartDate,
    this.recommendationFirstDate,
    this.recommendationExitPrice,
    this.recommendationResultStatus,
    this.recommendationResultPercentage,
    this.recommendationTitle,
    this.initialAmount,
    this.finalAmount,
    this.recommendationGrossResult,
    this.recommendationLongMovements,
    this.recommendationShortMovements,
    this.operationStartDate,
    this.operationInvestedAmount,
    this.operationTitle,
    this.operationTotalAmount,
    this.operationGain,
    this.operationLoss,
    this.operationAveragePrice,
    this.operationFees,
    this.operationNetResult,
    this.operationHadEnoughBalance = true,
    this.operationResult,
    this.operationMovements,
    this.closedPositionWasClosedEarly = false,
    this.closedPositionResultPercentage,
  });

  factory TradePosition.fromJson(Map<String, dynamic> json) => _$TradePositionFromJson(json);
}

@JsonSerializable(createToJson: false)
class TradePositionMovement {
  @JsonKey(name: 'tipomovimento')
  final String? movementType;
  @JsonKey(name: 'titulo')
  final String? title;
  @JsonKey(name: 'quantidade')
  final int? quantity;
  @JsonKey(name: 'precomedio')
  final double? averagePrice;
  @JsonKey(name: 'valorfinanceiro')
  final double? financialValue;
  @JsonKey(name: 'valorinicial')
  final double? initialValue;

  TradePositionMovement({
    this.movementType,
    this.title,
    this.quantity,
    this.averagePrice,
    this.financialValue,
    this.initialValue,
  });

  factory TradePositionMovement.fromJson(Map<String, dynamic> json) => _$TradePositionMovementFromJson(json);
}
