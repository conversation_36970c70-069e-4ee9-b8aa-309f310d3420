// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trade.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Trade _$TradeFromJson(Map<String, dynamic> json) => Trade(
      id: (json['id'] as num?)?.toInt(),
      firstName: json['nome'] as String?,
      lastName: json['sobrenome'] as String?,
      webImageUrl: json['fotoweb'] as String?,
      mobileImageUrl: json['fotoapp'] as String?,
      fullDescription: json['descricao'] as String?,
      shortDescription: json['descricaoestrategia'] as String?,
      performance: (json['rendimento'] as num?)?.toDouble(),
      successRate: (json['taxadeacerto'] as num?)?.toDouble(),
      recommendationAverage:
          (json['mediaporrecomendacoes'] as num?)?.toDouble(),
      monthsWithActivity: json['mesesatividade'] == null
          ? const []
          : Trade.parseDateFromYearAndMonthOnly(
              json['mesesatividade'] as List?),
      firstActivityDate: json['dataprimeirarecomendacao'] == null
          ? null
          : DateTime.parse(json['dataprimeirarecomendacao'] as String),
      hiredValuePerOperation: (json['valoroperacao'] as num?)?.toDouble(),
      minValuePerOperation: (json['valorminimo'] as num?)?.toDouble(),
      isActive: json['ativo'] as bool? ?? false,
      reports: (json['relatorios'] as List<dynamic>?)
              ?.map((e) => TradeReport.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      isHired: json['contratado'] as bool? ?? false,
      videoUrl: json['linkurl'] as String?,
      market: TradeMarketType.fromId((json['tipomercado'] as num?)?.toInt()),
      numberOfContracts: (json['numerodecontratos'] as num?)?.toInt(),
      isHiringAllowed: json['clienteimportado'] as bool? ?? true,
      hiringRequirementsText: json['requisitosdeativacao'] as String?,
      hiringRequirementsUrl: json['linkparceiro'] as String?,
      feePercentage: (json['corretagem'] as num?)?.toDouble(),
    );

TradeReport _$TradeReportFromJson(Map<String, dynamic> json) => TradeReport(
      title: json['titulo'] as String?,
      date: json['datapublicacao'] == null
          ? null
          : DateTime.parse(json['datapublicacao'] as String),
      url: json['link'] as String?,
    );
