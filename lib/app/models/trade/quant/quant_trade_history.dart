import 'package:json_annotation/json_annotation.dart';

import '../trade_history.dart';
import '../trade_position.dart';

part 'quant_trade_history.g.dart';

@JsonSerializable(createToJson: false)
class QuantTradeHistory extends TradeHistory {
  @override
  @Json<PERSON>ey(name: 'historicoestrategia')
  final List<TradePosition> recommendationsSpotFutures;

  QuantTradeHistory({
    super.recommendationsMonthResult,
    super.recommendationsYearPerformancePercentage,
    this.recommendationsSpotFutures = const [],
    super.recommendationsStructured,
  });

  factory QuantTradeHistory.fromJson(Map<String, dynamic> json) => _$QuantTradeHistoryFromJson(json);
}
