import 'package:json_annotation/json_annotation.dart';

import 'quant_trade.dart';

part 'quant_trade_list.g.dart';

@JsonSerializable(createToJson: false)
class QuantTradeList {
  @Json<PERSON>ey(name: 'estrategiasdisponiveis')
  final List<QuantTrade> availableStrategies;

  @JsonKey(name: 'estrategiascontratados')
  final List<QuantTrade> hiredStrategies;

  QuantTradeList({
    this.availableStrategies = const [],
    this.hiredStrategies = const [],
  });

  factory QuantTradeList.fromJson(Map<String, dynamic> json) => _$QuantTradeListFromJson(json);
}
