import 'package:json_annotation/json_annotation.dart';

import '../trade.dart';
import '../trade_market_type.dart';

part 'quant_trade.g.dart';

@JsonSerializable(createToJson: false)
class QuantTrade extends Trade {
  @override
  @Json<PERSON>ey(name: 'estrategiaativa')
  final bool isActive;

  QuantTrade({
    super.id,
    super.firstName,
    super.lastName,
    super.webImageUrl,
    super.mobileImageUrl,
    super.fullDescription,
    super.shortDescription,
    super.performance,
    super.successRate,
    super.recommendationAverage,
    super.monthsWithActivity,
    super.firstActivityDate,
    super.hiredValuePerOperation,
    super.minValuePerOperation,
    this.isActive = false,
    super.reports,
    super.isHired,
    super.videoUrl,
    super.market,
    super.numberOfContracts,
    super.isHiringAllowed,
    super.hiringRequirementsText,
    super.hiringRequirementsUrl,
  });

  factory QuantTrade.fromJson(Map<String, dynamic> json) => _$QuantTradeFrom<PERSON><PERSON>(json);
}
