// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quant_trade_history.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuantTradeHistory _$QuantTradeHistoryFromJson(Map<String, dynamic> json) =>
    QuantTradeHistory(
      recommendationsMonthResult: (json['resultadomes'] as num?)?.toDouble(),
      recommendationsYearPerformancePercentage:
          (json['rendimento'] as num?)?.toDouble(),
      recommendationsSpotFutures: (json['historicoestrategia']
                  as List<dynamic>?)
              ?.map((e) => TradePosition.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      recommendationsStructured:
          (json['historicoestruturadas'] as List<dynamic>?)
              ?.map((e) => TradePosition.fromJson(e as Map<String, dynamic>))
              .toList(),
    )
      ..recommendationsLast12MonthsResult =
          (json['resultadoultimos12meses'] as num?)?.toDouble()
      ..closedPositionsMonthInvestedAmount =
          (json['totalvaloraplicado'] as num?)?.toDouble()
      ..closedPositionsMonthResultAmount =
          (json['totalresultadomesvalor'] as num?)?.toDouble()
      ..closedPositionsMonthResultPercentage =
          (json['totalresultadomesporcentagem'] as num?)?.toDouble();
