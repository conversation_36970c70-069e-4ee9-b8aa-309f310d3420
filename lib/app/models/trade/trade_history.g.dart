// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trade_history.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TradeHistory _$TradeHistoryFromJson(Map<String, dynamic> json) => TradeHistory(
      recommendationsSpotFutures: (json['historico'] as List<dynamic>?)
          ?.map((e) => TradePosition.fromJson(e as Map<String, dynamic>))
          .toList(),
      recommendationsStructured:
          (json['historicoestruturadas'] as List<dynamic>?)
              ?.map((e) => TradePosition.fromJson(e as Map<String, dynamic>))
              .toList(),
      recommendationsMonthResult: (json['resultadomes'] as num?)?.toDouble(),
      recommendationsYearPerformancePercentage:
          (json['rendimento'] as num?)?.toDouble(),
      positionsStructured: (json['operacoesestruturadas'] as List<dynamic>?)
          ?.map((e) => TradePosition.fromJson(e as Map<String, dynamic>))
          .toList(),
      openPositionsSpotFutures: (json['operacoesabertas'] as List<dynamic>?)
          ?.map((e) => TradePosition.fromJson(e as Map<String, dynamic>))
          .toList(),
      closedPositionsSpotFutures:
          (json['operacoesencerradas'] as List<dynamic>?)
              ?.map((e) => TradePosition.fromJson(e as Map<String, dynamic>))
              .toList(),
      closedPositionsMonthInvestedAmount:
          (json['totalvaloraplicado'] as num?)?.toDouble(),
      closedPositionsMonthResultAmount:
          (json['totalresultadomesvalor'] as num?)?.toDouble(),
      closedPositionsMonthResultPercentage:
          (json['totalresultadomesporcentagem'] as num?)?.toDouble(),
    )..recommendationsLast12MonthsResult =
        (json['resultadoultimos12meses'] as num?)?.toDouble();
