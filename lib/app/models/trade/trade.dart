import 'dart:math';

import 'package:json_annotation/json_annotation.dart';

import '../../config/constants.dart';
import 'trade_market_type.dart';

part 'trade.g.dart';

@JsonSerializable(createToJson: false)
class Trade {
  final int? id;

  @Json<PERSON><PERSON>(name: 'nome')
  final String? firstName;

  @Json<PERSON>ey(name: 'sobrenome')
  final String? lastName;

  @JsonKey(name: 'fotoweb')
  final String? webImageUrl;

  @Json<PERSON>ey(name: 'fotoapp')
  final String? mobileImageUrl;

  @<PERSON>son<PERSON><PERSON>(name: 'descricao')
  final String? fullDescription;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'descricaoestrategia')
  final String? shortDescription;

  @J<PERSON><PERSON><PERSON>(name: 'rendimento')
  final double? performance;

  @JsonKey(name: 'taxadeacerto')
  final double? successRate;

  @Json<PERSON>ey(name: 'mediaporrecomendacoes')
  final double? recommendationAverage;

  @<PERSON>son<PERSON><PERSON>(name: 'mesesatividade', fromJson: parseDateFromYearAndMonthOnly)
  final List<DateTime> monthsWithActivity;

  @Json<PERSON><PERSON>(name: 'dataprimeirarecomendacao')
  final DateTime? firstActivityDate;

  @JsonKey(name: 'valorminimo')
  final double? minValuePerOperation;

  @JsonKey(name: 'valoroperacao')
  final double? hiredValuePerOperation;

  @JsonKey(name: 'ativo')
  final bool isActive;

  @JsonKey(name: 'relatorios')
  final List<TradeReport> reports;

  @JsonKey(name: 'linkurl')
  final String? videoUrl;

  @JsonKey(name: 'tipomercado', fromJson: TradeMarketType.fromId)
  final TradeMarketType? market;

  @JsonKey(name: 'numerodecontratos')
  final int? numberOfContracts;

  @JsonKey(name: 'contratado')
  final bool isHired;

  int get minimumNumberOfContracts => (minValuePerOperation ?? 0) ~/ Constants.tradeBmfContractValue;

  String get fullName => '${firstName ?? ''}${(lastName ?? '').isNotEmpty ? ' $lastName' : ''}';

  String get profileImageUrl => mobileImageUrl ?? webImageUrl ?? '';

  bool get hasHistory => monthsWithActivity.isNotEmpty;

  bool get isBmf => market == TradeMarketType.futures;

  bool get isStructuredOperation => market == TradeMarketType.structuredOperation;

  @JsonKey(name: 'clienteimportado')
  final bool isHiringAllowed;

  @JsonKey(name: 'requisitosdeativacao')
  final String? hiringRequirementsText;

  @JsonKey(name: 'linkparceiro')
  final String? hiringRequirementsUrl;

  @JsonKey(name: 'corretagem')
  final double? feePercentage;

  Trade({
    this.id,
    this.firstName,
    this.lastName,
    this.webImageUrl,
    this.mobileImageUrl,
    this.fullDescription,
    this.shortDescription,
    this.performance,
    this.successRate,
    this.recommendationAverage,
    this.monthsWithActivity = const [],
    this.firstActivityDate,
    this.hiredValuePerOperation,
    this.minValuePerOperation,
    this.isActive = false,
    this.reports = const [],
    this.isHired = false,
    this.videoUrl,
    this.market,
    this.numberOfContracts,
    this.isHiringAllowed = true,
    this.hiringRequirementsText,
    this.hiringRequirementsUrl,
    this.feePercentage,
  });

  /// Faz o parse de datas no formato 'MM/yyyy' para DateTime.
  static List<DateTime> parseDateFromYearAndMonthOnly(List? yearsAndMonths) {
    return (yearsAndMonths ?? []).map((yearAndMonth) {
      final parts = yearAndMonth.toString().split('/');
      return DateTime(int.parse(parts[1]), int.parse(parts[0]));
    }).toList();
  }

  factory Trade.fromJson(Map<String, dynamic> json) => _$TradeFromJson(json);
}

@JsonSerializable(createToJson: false)
class TradeReport {
  @JsonKey(name: 'titulo')
  final String? title;
  @JsonKey(name: 'datapublicacao')
  final DateTime? date;
  @JsonKey(name: 'link')
  final String? url;

  TradeReport({this.title, this.date, this.url});

  factory TradeReport.fromJson(Map<String, dynamic> json) => _$TradeReportFromJson(json);
}

extension SortByPerformance on List<Trade> {
  /// Ordena por maior [performance].
  List<Trade> get sortedByPerformance {
    return this..sort((a, b) => (b.performance ?? 0).compareTo(a.performance ?? 0));
  }

  /// Retorna os trades com [performance] positivo ou zero.
  List<Trade> get positivePerformance {
    return where((trade) => (trade.performance ?? 0) >= 0).toList();
  }

  /// Retorna os trades com [performance] negativo.
  List<Trade> get negativePerformance {
    return where((trade) => (trade.performance ?? 0) < 0).toList();
  }

  /// Alterna entre Mercado à Vista e Mercado Futuro.
  List<Trade> get alteranatedByMarket {
    // Separa os trades por tipo de mercado
    final spotTrades = where((trade) => !trade.isBmf).toList();
    if (spotTrades.isEmpty) return this;
    final futuresTrades = where((trade) => trade.isBmf).toList();
    if (futuresTrades.isEmpty) return this;

    // Alterna os trades por tipo de mercado, começando por BMF
    List<Trade> result = [];
    for (var index = 0; index < max(spotTrades.length, futuresTrades.length); index++) {
      if (index < spotTrades.length) result.add(spotTrades[index]);
      if (index < futuresTrades.length) result.add(futuresTrades[index]);
    }
    return result;
  }
}
