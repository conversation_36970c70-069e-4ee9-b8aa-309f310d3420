import 'package:json_annotation/json_annotation.dart';

part 'notification_new_functionality.g.dart';

@JsonSerializable()
class NotificationNewFunctionality {
  @J<PERSON><PERSON><PERSON>(name: 'novafuncionalidadeid')
  final int? id;
  @J<PERSON><PERSON>ey(name: 'novafuncionalidadenome')
  final String? name;
  @Json<PERSON>ey(name: 'novafuncionalidadedtinicio')
  final DateTime? startDate;
  @Json<PERSON>ey(name: 'novafuncionalidadestatus')
  bool? status;

  NotificationNewFunctionality({
    this.id,
    this.name,
    this.startDate,
    this.status = false,
  });

  factory NotificationNewFunctionality.fromJson(Map<String, dynamic> json) => _$NotificationNewFunctionalityFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationNewFunctionalityToJson(this);
}
