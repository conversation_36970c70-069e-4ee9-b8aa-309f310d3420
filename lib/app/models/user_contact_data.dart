import 'package:json_annotation/json_annotation.dart';

part 'user_contact_data.g.dart';

@JsonSerializable(createToJson: false)
class UserContactData {
  @JsonKey(name: 'email')
  final String? email;

  @JsonKey(name: 'telefone')
  final String? phoneNumber;

  UserContactData({
    this.email,
    this.phoneNumber,
  });

  factory UserContactData.fromJson(Map<String, dynamic> json) => _$UserContactDataFromJson(json);
}
