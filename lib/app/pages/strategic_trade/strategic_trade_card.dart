import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../config/app_theme.dart';
import '../../utils/extensions.dart';
import '../../widgets/info/badge.dart';

const _defaultPadding = EdgeInsets.symmetric(horizontal: 16, vertical: 8);
const _borderColor = AppTheme.blueColor5;
const _horizontalDivider = Divider(height: 1, color: _borderColor);
const _verticalDivider = VerticalDivider(width: 1, color: _borderColor);

class StrategicTradeCard extends StatelessWidget {
  final String? operation;
  final String? type;
  final double? maximumReturn;
  final double? maximumReturnPercentage;
  final double? minimumInvestment;
  final DateTime? dueDate;
  final String? vies;
  final void Function()? onTap;
  final void Function()? onWhatsappButtonPressed;

  const StrategicTradeCard({
    this.operation,
    this.type,
    this.maximumReturn,
    this.maximumReturnPercentage,
    this.minimumInvestment,
    this.dueDate,
    this.vies,
    this.onTap,
    this.onWhatsappButtonPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.blueColor7.withOpacity(0.3),
          borderRadius: BorderRadius.circular(5),
          border: Border.all(color: _borderColor),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 50,
              color: AppTheme.blueColorCardBg3,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: CMBadge(
                      margin: const EdgeInsets.only(top: 5, bottom: 8, left: 16, right: 16),
                      backgroundColor: AppTheme.greenColor,
                      text: vies ?? '',
                      isTextUpperCase: true,
                      textStyle: AppTheme.bold11White.copyWith(color: AppTheme.greenColor3),
                    ),
                  ),
                  Row(
                    children: [
                      _verticalDivider,
                      IconButton(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        icon: const Icon(Icons.description_outlined, color: Colors.white, size: 28),
                        visualDensity: VisualDensity.compact,
                        onPressed: onTap,
                      ),
                      _verticalDivider,
                      IconButton(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        icon: SvgPicture.asset('assets/icons/action_whatsapp.svg', width: 30, height: 30),
                        visualDensity: VisualDensity.compact,
                        onPressed: onWhatsappButtonPressed,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            _horizontalDivider,
            Container(
              height: 60,
              padding: _defaultPadding,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: _Information(label: 'Operação', value: operation ?? ''),
                  ),
                  Expanded(
                    child: _Information(label: 'Tipo', value: type ?? ''),
                  ),
                ],
              ),
            ),
            _horizontalDivider,
            Container(
              padding: _defaultPadding,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: _Information(
                          label: 'Retorno máximo/lote',
                          value: maximumReturn.asCurrency(),
                        ),
                      ),
                      Expanded(
                        child: _Information(
                          label: 'Retorno máximo(%)',
                          value: (maximumReturnPercentage ?? 0).asPercentage(decimalDigits: 0, showPlusSign: true),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: _Information(label: 'Aporte mínimo(lote)', value: minimumInvestment.asCurrency()),
                      ),
                      Expanded(
                        child: _Information(label: 'Vencimento', value: dueDate?.toddMMyyyy() ?? ''),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _Information extends StatelessWidget {
  final String label;
  final String value;

  const _Information({
    required this.label,
    required this.value,
  });

  Widget get _labelWidget => Text(label, style: AppTheme.regular11White);
  Widget get _valueWidget => Text(value, style: AppTheme.semi14White, maxLines: 1, overflow: TextOverflow.ellipsis);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _labelWidget,
        _valueWidget,
      ],
    );
  }
}
