import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../api/suitability/suitability_api.dart';
import '../../config/app_theme.dart';
import '../../controllers/strategic_trade/strategic_information_page_controller.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/info/distributor_fee_disclaimer.dart';
import '../../widgets/info/horizontal_field.dart';
import '../../widgets/info/vertical_field.dart';
import '../../widgets/scaffold/app_scaffold.dart';
import 'strategic_list_page.dart';
import '../../utils/extensions.dart';

class StrategicInformationPage extends StatelessWidget {
  const StrategicInformationPage({super.key});

  static const routeName = '${StrategicListPage.routeName}/information';

  @override
  Widget build(BuildContext context) {
    return GetBuilder<StrategicInformationPageController>(
      init: StrategicInformationPageController(),
      builder: (controller) {
        return AppScaffold(
          appBar: const TopBar(
            title: 'Trade Estratégico',
          ),
          extendBody: true,
          resizeToAvoidBottomInset: true,
          body: CMBody(
            onGetData: controller.loadStrategicTrade,
            loadingText: 'Carregando operação...',
            errorText: 'Erro ao carregar, confira novamente mais tarde.',
            ignoreEmptyState: true,
            child: Theme(
              data: ThemeData(
                textTheme: TextTheme(
                  labelSmall: AppTheme.regular12Orange.copyWith(fontWeight: FontWeight.w400),
                  bodyLarge: AppTheme.bold14White,
                ),
              ),
              child: SingleChildScrollView(
                padding: AppTheme.pagePadding,
                child: Form(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      VerticalField(
                        'Operação',
                        controller.selectedStrategic.value?.title ?? '',
                        widthFactor: 1,
                      ),
                      HorizontalField('Viés', controller.selectedStrategic.value?.vies ?? ''),
                      HorizontalField(
                        'Estrutura',
                        controller.selectedStrategic.value?.structure ?? '',
                      ),
                      HorizontalField(
                        'Mercado',
                        controller.selectedStrategic.value?.mercado ?? '',
                      ),
                      HorizontalField(
                        'Perfil do investidor',
                        UserSuitabilityProfile.aggressive.text,
                      ),
                      HorizontalField(
                        'Aporte Mínimo (lote)',
                        (controller.selectedStrategic.value?.minimumInvestment ?? 0).asCurrency(),
                      ),
                      HorizontalField(
                        'Retorno máximo/lote',
                        (controller.selectedStrategic.value?.maximumReturn ?? 0).asCurrency(),
                      ),
                      HorizontalField(
                        'Retorno máximo(%)',
                        (controller.selectedStrategic.value?.maximumReturnPercentage ?? 0).asPercentage(decimalDigits: 0, showPlusSign: true),
                      ),
                      HorizontalField(
                        'Risco máximo(%)',
                        (controller.selectedStrategic.value?.maximumRiskPercentage ?? 0).asPercentage(decimalDigits: 0, showPlusSign: true),
                      ),
                      const SizedBox(height: 30),
                      SizedBox(
                        width: double.infinity,
                        height: 54,
                        child: ElevatedButton(
                          onPressed: () => controller.onWhatsappButtonPressed(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.orangeColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(5),
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Text(
                                'INVISTA COM UM ESPECIALISTA',
                                style: AppTheme.medium14White,
                              ),
                              const SizedBox(width: 8),
                              SvgPicture.asset('assets/icons/action_whatsapp.svg', width: 26, height: 26),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),
                      Button.outlined(
                        text: 'DETALHES DA OPERAÇÃO',
                        isTextUpperCase: true,
                        onPressed: () async => controller.selectedStrategic.value?.document != null
                            ? controller.openFile(controller.selectedStrategic.value!.document.toString(), 'Detalhes - ${controller.selectedStrategic.value?.title ?? ''}')
                            : controller.onOpenDocumentNullWarnning(),
                      ),
                      const SizedBox(height: 10),
                      Button.outlined(
                        text: 'TESE DA OPERAÇÃO',
                        isTextUpperCase: true,
                        onPressed: () => controller.selectedStrategic.value?.tese != null
                            ? controller.openFile(controller.selectedStrategic.value!.tese.toString(), 'Tese - ${controller.selectedStrategic.value?.title ?? ''}')
                            : controller.onOpenDocumentNullWarnning(),
                      ),
                      const SizedBox(height: 30),
                      HorizontalField(
                        'Resumo da operação',
                        controller.selectedStrategic.value?.description ?? '',
                        valueStyle: AppTheme.regular10White,
                      ),
                      DistributorFeeDisclaimer(
                        feeText: controller.distributorFeeText.value,
                        padding: const EdgeInsets.only(top: 10, bottom: 30),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
