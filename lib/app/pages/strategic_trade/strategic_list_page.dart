import 'package:cm_capital_app/app/pages/strategic_trade/strategic_trade_card.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controllers/strategic_trade/strategic_list_page_controller.dart';
import '../../widgets/buttons/info_button.dart';
import '../../widgets/loading/pull_to_refresh.dart';
import '../../widgets/scaffold/app_scaffold.dart';
import 'strategic_trade_how_it_works_page.dart';

class StrategicListPage extends StatelessWidget {
  const StrategicListPage({super.key});

  static const routeName = '/meus-investimentos/trade-estrategico';

  @override
  Widget build(BuildContext context) {
    return GetBuilder<StrategicListPageController>(
      init: StrategicListPageController(),
      builder: (controller) {
        return AppScaffold(
          extendBody: true,
          appBar: TopBar(
            title: 'Trade Estratégico',
            icon: InfoButton(onPressed: () {
              Get.toNamed(StrategicTradeHowItWorksPage.routeName);
            }),
          ),
          body: CMBody(
            onGetData: controller.loadStrategicTrade,
            loadingText: 'Carregando operações disponíveis...',
            errorText: 'Não há operações disponíveis no momento, confira novamente mais tarde.',
            child: PullToRefresh(
              onRefresh: controller.loadStrategicTrade,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Container(
                        margin: const EdgeInsets.only(top: 20, bottom: 10),
                        width: double.infinity,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(5),
                          child: Image.asset(
                            'assets/images/strategic_trade_banner.jpg',
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: controller.strategicList.length,
                        separatorBuilder: (_, __) => const SizedBox(height: 20),
                        itemBuilder: (_, index) {
                          final entry = controller.strategicList[index];
                          return StrategicTradeCard(
                            operation: entry.title,
                            type: entry.mercado,
                            maximumReturn: entry.maximumReturn,
                            maximumReturnPercentage: entry.maximumReturnPercentage,
                            minimumInvestment: entry.minimumInvestment,
                            dueDate: entry.dueDate,
                            vies: entry.vies,
                            onTap: () => controller.showDetails(entry.id ?? 0),
                            onWhatsappButtonPressed: () async => await controller.onWhatsappButtonPressed(entry.title ?? ''),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
