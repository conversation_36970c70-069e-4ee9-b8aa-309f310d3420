import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../widgets/scaffold/app_scaffold.dart';
import 'strategic_list_page.dart';

class StrategicTradeHowItWorksPage extends StatelessWidget {
  const StrategicTradeHowItWorksPage({super.key});

  static const routeName = '${StrategicListPage.routeName}/como-funciona';

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: const TopBar(title: 'Como funciona?'),
      extendBody: true,
      body: CMBody(
        child: ListView(
          padding: AppTheme.pagePadding,
          children: const [
            Text(
              'Oferecemos estratégias de médio prazo para que você possa lucrar em diferentes cenários de movimentação do mercado! Converse com nosso especialista clicando no botão "Invista" e escolha as estratégias e operações que fazem mais sentido para a sua carteira. Depois, é só acompanhar, pois as operações serão realizadas para você pelo nosso time de forma recorrente, seguindo a tese de cada operação.'
              '\n\nToque em qualquer card para obter informações detalhadas sobre a estratégia, incluindo os riscos e os documentos de tese. No momento, este produto é recomendado apenas para investidores com perfil Agressivo.',
            ),
          ],
        ),
      ),
    );
  }
}
