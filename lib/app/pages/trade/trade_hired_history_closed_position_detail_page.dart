import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../controllers/trade/trade_history_controller.dart';
import '../../models/trade/trade_position.dart';
import '../../utils/extensions.dart';
import '../../widgets/card/trade_structured_operation_item_card.dart';
import '../../widgets/info/amount_colored_chip.dart';
import '../../widgets/info/horizontal_field.dart';
import '../../widgets/list/app_scrollbar.dart';
import '../../widgets/scaffold/app_scaffold.dart';

class TradeHiredHistoryClosedPositionDetailPage extends StatelessWidget {
  const TradeHiredHistoryClosedPositionDetailPage({super.key});

  double? get _feePercentage => Get.isRegistered<TradeHistoryController>() ? Get.find<TradeHistoryController>().trade.feePercentage : null;
  String get _feePercentageText => 'Taxa${(_feePercentage ?? 0) > 0 ? ' (${_feePercentage.asPercentage()} do volume total)' : ''}';

  @override
  Widget build(BuildContext context) {
    final TradePosition? position = Get.arguments;

    return AppScaffold(
      appBar: const TopBar(title: 'Detalhes'),
      extendBody: true,
      body: CMBody(
        child: AppScrollbar(
          child: SingleChildScrollView(
            padding: AppTheme.pagePadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Informações
                HorizontalField('Operação', position?.operationTitle ?? '', padding: const EdgeInsets.only(bottom: 8)),
                const HorizontalField('Status', 'Encerrada', padding: EdgeInsets.only(bottom: 8)),
                HorizontalField('Data de início', position?.operationStartDate?.toddMMyyyy() ?? '', padding: const EdgeInsets.only(bottom: 8)),
                HorizontalField('Data de encerramento', position?.endDate?.toddMMyyyy() ?? '', padding: const EdgeInsets.only(bottom: 8)),
                const Divider(color: Colors.white54, height: 33, thickness: 1),
                HorizontalField('Valor final', position?.finalAmount?.asCurrency() ?? '', padding: const EdgeInsets.only(bottom: 8)),
                HorizontalField('Valor inicial', position?.initialAmount?.asCurrency() ?? '', padding: const EdgeInsets.only(bottom: 8)),
                HorizontalField(_feePercentageText, position?.operationFees?.asCurrency() ?? '', padding: const EdgeInsets.only(bottom: 8)),
                HorizontalField.widget(
                  label: 'Resultado líquido (sem impostos)',
                  padding: const EdgeInsets.only(bottom: 24),
                  child: AmountColoredChip(
                    position?.operationNetResult ?? 0,
                    margin: const EdgeInsets.only(top: 2),
                    fontSize: 14,
                    showPlusSign: true,
                    showAsterisk: position?.closedPositionWasClosedEarly ?? false,
                  ),
                ),

                // Lista
                const Text('Movimentações', style: AppTheme.regular14White),
                const SizedBox(height: 8),
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  separatorBuilder: (_, __) => const SizedBox(height: 16),
                  itemCount: (position?.operationMovements ?? []).length,
                  itemBuilder: (_, index) {
                    final movement = position!.operationMovements![index];
                    return TradeStructuredOperationItemCard(
                      ticker: movement.title ?? '',
                      quantity: movement.quantity?.asPtBrDecimal ?? '',
                      averagePrice: movement.averagePrice?.asCurrency() ?? '',
                      operationType: movement.movementType ?? '',
                      result: movement.financialValue?.toMaskedAmount(showPlusSign: true) ?? '',
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
