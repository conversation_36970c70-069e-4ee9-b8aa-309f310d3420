import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../models/trade/trade.dart';
import '../../utils/extensions.dart';
import '../../utils/get_file.dart';
import '../../widgets/media/youtube_player.dart';
import '../../widgets/scaffold/app_scaffold.dart';

abstract class TradeDescriptionPage extends StatelessWidget {
  const TradeDescriptionPage({super.key});

  String get reportsTitle;

  @override
  Widget build(BuildContext context) {
    final trade = Get.arguments as Trade?;
    final videoCode = trade?.videoUrl.youtubeVideoCode ?? '';
    return CMYouTubePlayer(
      videoCode: videoCode,
      builder: (_, player) => AppScaffold(
        appBar: TopBar(title: trade?.fullName ?? ''),
        extendBody: true,
        body: CMBody(
          child: SingleChildScrollView(
            child: <PERSON>umn(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //Video
                Padding(
                  padding: AppTheme.pagePadding.copyWith(bottom: 0),
                  child: ClipRRect(
                    borderRadius: const BorderRadius.all(Radius.circular(10)),
                    child: videoCode.isEmpty
                        ? Container(
                            height: 200,
                            color: Colors.black,
                            child: const Center(
                              child: Text('O vídeo não está disponível no momento', style: AppTheme.regular14Grey3),
                            ),
                          )
                        : player,
                  ),
                ),

                // Descrição
                Padding(
                  padding: AppTheme.pagePadding.copyWith(bottom: 30),
                  child: Text(trade?.fullDescription ?? ''),
                ),

                // Relatórios
                if (trade?.reports.isNotEmpty ?? false) ...[
                  // Título
                  Padding(
                    padding: AppTheme.horizontalPadding,
                    child: Text(reportsTitle, style: AppTheme.bold12White),
                  ),

                  // Cabeçalho
                  Padding(
                    padding: AppTheme.horizontalPadding.copyWith(top: 10, bottom: 2),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Título', style: AppTheme.regular11Orange),
                        Text('Data', style: AppTheme.regular11Orange),
                      ],
                    ),
                  ),
                  const Divider(height: 1, color: Colors.white10, thickness: 1, indent: 25, endIndent: 25),

                  // Lista
                  ListView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    itemCount: trade!.reports.length,
                    itemBuilder: (_, index) {
                      final report = trade.reports[index];
                      return _Report(report: report, lineColor: index.isOdd ? const Color(0xFF052A45) : null);
                    },
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _Report extends StatefulWidget {
  final TradeReport report;
  final Color? lineColor;
  const _Report({required this.report, this.lineColor});
  @override
  State<_Report> createState() => __ReportState();
}

class __ReportState extends State<_Report> {
  var _isDownloading = false;

  Future<void> _download() async {
    if (widget.report.url case String url) {
      setState(() => _isDownloading = true);
      final fileExtension = url.split('.').last;
      downloadAndOpenFile(url, '${widget.report.title ?? 'relatorio'}.$fileExtension');
      Future.delayed(const Duration(milliseconds: 500), () => setState(() => _isDownloading = false));
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _download,
      child: Container(
        color: widget.lineColor,
        constraints: const BoxConstraints(minHeight: 40),
        padding: const EdgeInsets.symmetric(horizontal: 14),
        child: Row(
          children: [
            if (_isDownloading) const SizedBox.square(dimension: 24, child: CircularProgressIndicator(valueColor: AlwaysStoppedAnimation(AppTheme.orangeColor), strokeWidth: 2)),
            if (!_isDownloading) const Icon(Icons.file_download_outlined, color: AppTheme.orangeColor),
            const SizedBox(width: 10),
            Expanded(child: Text(widget.report.title ?? '', style: AppTheme.semi12Orange)),
            const SizedBox(width: 10),
            Text(widget.report.date?.toddMMyyyy() ?? '', style: AppTheme.semi11White),
          ],
        ),
      ),
    );
  }
}
