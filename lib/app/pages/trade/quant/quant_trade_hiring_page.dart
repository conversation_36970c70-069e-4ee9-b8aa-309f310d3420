import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/quant/quant_trade_hiring_page_controller.dart';
import 'quant_trade_list_page.dart';
import '../trade_hiring_page.dart';

class QuantTradeHiringPage extends StatelessWidget {
  const QuantTradeHiringPage({super.key});

  static const routeName = '${QuantTradeListPage.routeName}/contratacao';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: QuantTradeHiringPageController(),
      builder: (controller) {
        return TradeHiringPage(controller: controller);
      },
    );
  }
}
