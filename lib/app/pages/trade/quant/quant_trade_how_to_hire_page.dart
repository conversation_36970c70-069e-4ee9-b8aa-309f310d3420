import 'package:get/get.dart';

import '../../../controllers/trade/quant/quant_trade_list_page_controller.dart';
import 'quant_trade_list_page.dart';
import '../trade_how_to_hire_page.dart';

class QuantTradeHowToHirePage extends TradeHowToHirePage {
  const QuantTradeHowToHirePage({super.key});

  static const routeName = '${QuantTradeListPage.routeName}/como-contratar';

  @override
  String get title => 'Como contratar estratégias?';

  @override
  String get bodyText =>
      'Trade Quant é um serviço que te ajuda a realizar operações de renda variável automaticamente, com base nas recomendações de estratégias selecionadas. Clique no botão abaixo para conhecer as estratégias disponíveis.';

  @override
  String get buttonText => 'Estratégias Disponíveis';

  @override
  void onButtonPressed() {
    Get.find<QuantTradeListPageController>().tabController.animateTo(0);
    Get.toNamed(QuantTradeListPage.routeName);
  }
}
