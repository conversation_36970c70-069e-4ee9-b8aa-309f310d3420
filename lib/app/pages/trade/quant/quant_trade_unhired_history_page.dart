import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/quant/quant_trade_unhired_history_page_controller.dart';
import 'quant_trade_list_page.dart';
import '../trade_unhired_history_page.dart';

class QuantTradeUnhiredHistoryPage extends StatelessWidget {
  const QuantTradeUnhiredHistoryPage({super.key});

  static const routeName = '${QuantTradeListPage.routeName}/recomendacoes';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: QuantTradeUnhiredHistoryPageController(),
      builder: (controller) {
        return TradeUnhiredHistoryPage(controller: controller);
      },
    );
  }
}
