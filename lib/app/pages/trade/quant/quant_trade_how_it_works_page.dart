import 'quant_trade_list_page.dart';
import '../trade_how_it_works_page.dart';

class QuantTradeHowItWorksPage extends TradeHowItWorksPage {
  const QuantTradeHowItWorksPage({super.key});

  static const routeName = '${QuantTradeListPage.routeName}/como-funciona';

  @override
  String get bodyText => 'O Trade Quant é o serviço indicado para quem deseja potencializar resultados, mas não tem tempo ou conhecimento para operar com segurança.'
      '\n\nAo ativar o Trade Quant, você segue estratégias consolidadas no mercado e opera de forma automatizada, facilitando o seu dia a dia.'
      '\n\nConfira as estratégias disponíveis, objetivos e o histórico de cada uma e escolha a sua favorita para começar!';

  @override
  String get bmfDecoratedText =>
      '{Atenção:} Ao ativar o Trade Quant BMF, você não poderá alocar limites para operações manuais no mercado futuro. Caso deseje operar contratos como WIN e WDO manualmente, além das operações automatizadas pelo Trade Quant BMF, será necessário entrar em contato com a nossa equipe de atendimento para ajustes.';
}
