import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/quant/quant_trade_hired_history_month_page_controller.dart';
import 'quant_trade_hired_history_page.dart';
import '../trade_hired_history_month_page.dart';

class QuantTradeHiredHistoryMonthPage extends StatelessWidget {
  const QuantTradeHiredHistoryMonthPage({super.key});

  static const routeName = '${QuantTradeHiredHistoryPage.routeName}/encerradas/mes';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: QuantTradeHiredHistoryMonthPageController(),
      builder: (controller) {
        return TradeHiredHistoryMonthPage(
          controller: controller,
        );
      },
    );
  }
}
