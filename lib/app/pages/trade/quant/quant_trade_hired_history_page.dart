import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/quant/quant_trade_hired_history_page_controller.dart';
import 'quant_trade_list_page.dart';
import '../trade_hired_history_page.dart';

class QuantTradeHiredHistoryPage extends StatelessWidget {
  const QuantTradeHiredHistoryPage({super.key});

  static const routeName = '${QuantTradeListPage.routeName}/operacoes';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: QuantTradeHiredHistoryPageController(),
      builder: (controller) {
        return TradeHiredHistoryPage(controller: controller);
      },
    );
  }
}
