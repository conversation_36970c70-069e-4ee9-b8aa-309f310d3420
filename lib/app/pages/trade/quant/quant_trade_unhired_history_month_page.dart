import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/quant/quant_trade_unhired_history_month_page_controller.dart';
import 'quant_trade_unhired_history_page.dart';
import '../trade_unhired_history_month_page.dart';

class QuantTradeUnhiredHistoryMonthPage extends StatelessWidget {
  const QuantTradeUnhiredHistoryMonthPage({super.key});

  static const routeName = '${QuantTradeUnhiredHistoryPage.routeName}/mes';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: QuantTradeUnhiredHistoryMonthPageController(),
      builder: (controller) {
        return TradeUnhiredHistoryMonthPage(
          controller: controller,
        );
      },
    );
  }
}
