import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../tabs/home-menu/my_investments_page.dart';
import 'quant_trade_how_to_hire_page.dart';
import 'quant_trade_list_page.dart';
import '../../../controllers/trade/quant/quant_trade_list_page_controller.dart';
import '../trade_my_list_page.dart';

class QuantTradeMyListPage extends StatelessWidget {
  const QuantTradeMyListPage({super.key});

  static const routeName = '${MyInvestmentsPage.routeName}${QuantTradeListPage.routeName}';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: QuantTradeListPageController(),
      builder: (controller) {
        return TradeMyListPage(
          controller: controller,
          pageTitle: 'Trade Quant',
          infoPageRouteName: QuantTradeHowToHirePage.routeName,
          pageSubtitle: 'Estratégias Contratadas',
          emptyListTitleText: 'Você ainda não tem nenhuma estratégia contratada.',
          emptyListBodyText:
              'Trade Quant é um serviço que te ajuda a realizar operações de renda variável automaticamente, com base nas recomendações de estratégias selecionadas. Clique no botão abaixo para conhecer as estratégias disponíveis.',
          emptyListButtonText: 'Contratar Estratégias',
          listPageRouteName: QuantTradeListPage.routeName,
        );
      },
    );
  }
}
