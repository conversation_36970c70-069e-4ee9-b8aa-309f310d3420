import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../config/constants.dart';
import '../../controllers/trade/trade_hired_history_month_page_controller.dart';
import '../../utils/extensions.dart';
import '../../widgets/card/trade_closed_position_structured_operation_card.dart';
import '../../widgets/card/trade_history_spot_futures_card.dart';
import '../../widgets/list/trade_fixed_header_list.dart';
import '../../widgets/scaffold/app_scaffold.dart';

class TradeHiredHistoryMonthPage extends StatelessWidget {
  final TradeHiredHistoryMonthPageController controller;

  const TradeHiredHistoryMonthPage({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: const TopBar(title: 'Histórico de Operações'),
      extendBody: true,
      body: CMBody(
        onGetData: controller.loadMonthHistory,
        errorText: 'Não foi possível carregar as operações do mês.',
        ignoreEmptyState: true,
        child: TradeFixedHeaderList(
          fullWidthLabel: controller.tradeLabel,
          fullWidthValue: controller.trade?.fullName ?? '',
          topLeftLabel: 'Mês',
          topLeftValue: controller.selectedMonth.monthDate.toMonthAndYear(),
          topRightLabel: controller.performanceLabel,
          topRightValue: controller.performanceChip,
          bottomLeftLabel: controller.investedLabel,
          bottomLeftValue: controller.investedAmount,
          bottomRightLabel: controller.resultLabel,
          bottomRightValue: controller.resultAmount,
          totalItems: controller.monthHistory?.closedPositions.length ?? 0,
          itemBuilder: (_, index) {
            final position = controller.monthHistory!.closedPositions[index];
            if (controller.trade?.isStructuredOperation == true) {
              return TradeClosedPositionStructuredOperationCard(
                position: position,
                onDetailsPressed: () => Get.toNamed(controller.closedPositionDetailRouteName, arguments: position),
              );
            } else {
              return TradeHistorySpotFuturesCard(
                ticker: position.ticker,
                positionType: position.positionType,
                isBmf: controller.trade?.isBmf == true,
                performance: position.closedPositionResultPercentage,
                startDate: position.operationStartDate,
                endDate: position.endDate,
                isHired: true,
                entryAmount: position.operationInvestedAmount,
                exitAmount: position.operationResult,
                numberOfContracts: position.numberOfContracts,
                hadEnoughBalance: position.operationHadEnoughBalance,
                wasClosedEarly: position.closedPositionWasClosedEarly,
              );
            }
          },
          listBottomInformation: Text(
            'Operações com “*” foram encerradas antecipadamente ${controller.informationReferenceText}, buscando potencializar ganhos e minimizar riscos de acordo com eventos extraordinários do mercado.\n\n'
            'As ordens são executadas na abertura do mercado, no regime de melhores esforços, ou seja, por condições do próprio mercado poderá existir diferenças de valor entre o preço de entrada/saída recomendado ${controller.informationReferenceText} em relação ao valor executado pela CM CAPITAL.\n\n'
            'Adicionalmente, informamos que todos os preços e resultados apresentados não consideram os proventos pagos pelo ativo no decorrer da operação.'
            '${controller.trade?.isBmf == true ? '\n\nCada contrato corresponde a ${Constants.tradeBmfContractValue.asCurrency()}' : ''}',
            style: AppTheme.regular12White,
          ),
        ),
      ),
    );
  }
}
