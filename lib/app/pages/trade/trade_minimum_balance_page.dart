import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../controllers/trade/trade_hiring_page_controller.dart';
import '../../widgets/scaffold/app_scaffold.dart';

class TradeMinimumBalancePage extends StatelessWidget {
  const TradeMinimumBalancePage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.arguments as TradeHiringPageController;

    final title = '${controller.balanceText} necessário para ${controller.trade.isActive ? 'editar' : 'ativar'} o serviço';

    // Texto inicial
    var text = 'O ${controller.balanceText.toLowerCase()} disponível necessário é equivalente a ${controller.equationMultiplier.value}';
    final blockedBalanceText = 'O ${controller.balanceText.toLowerCase()} não será bloqueado.';

    // Edição
    if (controller.trade.isActive == true) {
      // Novo valor inferior ao atual
      if (controller.minimumBalanceToInvest.value <= 0) {
        text = '*Se o novo valor é inferior ao valor atual, o ${controller.balanceText.toLowerCase()} disponível necessário será R\$ 0,00.';
        // Novo valor maior que o atual
      } else {
        text += ' vezes a diferença entre os valores.';
        if (controller.showBalanceUnblockedText) text += '\n\n$blockedBalanceText';
      }

      // Ativação
    } else {
      text += ' operações.';
      if (controller.showBalanceUnblockedText) text += ' $blockedBalanceText';
    }

    return AppScaffold(
      appBar: TopBar(title: title),
      extendBody: true,
      body: CMBody(
        child: SingleChildScrollView(
          padding: AppTheme.pagePadding,
          child: Text(text),
        ),
      ),
    );
  }
}
