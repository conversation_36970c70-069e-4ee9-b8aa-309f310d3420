import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../config/constants.dart';
import '../../widgets/card/side_border_card.dart';
import '../../widgets/info/decorated_text.dart';
import '../../widgets/scaffold/app_scaffold.dart';

abstract class TradeHowItWorksPage extends StatelessWidget {
  const TradeHowItWorksPage({super.key});

  String get bodyText;
  String? get bmfDecoratedText;

  @override
  Widget build(BuildContext context) {
    final isBmf = Get.arguments as bool?;
    return AppScaffold(
      appBar: const TopBar(title: 'Como funciona?'),
      extendBody: true,
      body: CMBody(
        child: ListView(
          padding: AppTheme.pagePadding,
          children: [
            Text(bodyText, style: AppTheme.regular14White),
            SideBorderCard(
              margin: const EdgeInsets.only(top: 20),
              borderColor: AppTheme.greenColor3,
              child: DecoratedText(
                '${Constants.tradeBalanceWarningDecoratedText}'
                '${isBmf == true && (bmfDecoratedText ?? '').isNotEmpty ? '\n\n$bmfDecoratedText' : ''}',
                decoratedTextStyle: AppTheme.bold14White,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
