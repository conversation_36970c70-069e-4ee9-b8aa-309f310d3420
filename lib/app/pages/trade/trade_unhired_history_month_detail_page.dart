import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../models/trade/trade_position.dart';
import '../../utils/extensions.dart';
import '../../widgets/info/border_info.dart';
import '../../widgets/info/gain_loss_chip.dart';
import '../../widgets/list/trade_fixed_header_list.dart';
import '../../widgets/scaffold/app_scaffold.dart';

const _borderColor = BorderInfo.defaultBorderColor;
const _verticalDivider = VerticalDivider(width: 1, color: _borderColor);

abstract class TradeUnhiredHistoryMonthDetailPage extends StatelessWidget {
  const TradeUnhiredHistoryMonthDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    final TradePosition? operation = Get.arguments;
    final buyList = operation?.recommendationLongMovements ?? [];
    final sellList = operation?.recommendationShortMovements ?? [];
    final totalItems = max(buyList.length, sellList.length);

    return AppScaffold(
      appBar: const TopBar(title: 'Detalhes'),
      extendBody: true,
      body: CMBody(
        child: TradeFixedHeaderList(
          fullWidthLabel: 'Operação',
          fullWidthValue: operation?.recommendationTitle ?? '',
          topLeftLabel: 'Data da operação',
          topLeftValue: operation?.operationStartDate?.toddMMyyyy() ?? '',
          topRightLabel: 'GainLoss',
          topRightValue: GainLossChip(operation?.recommendationResultPercentage ?? 0),
          totalItems: totalItems + 1,
          listBoxDecoration: BoxDecoration(border: Border.all(color: _borderColor), borderRadius: BorderRadius.circular(5)),
          listSeparator: const Divider(height: 1, color: _borderColor),
          itemBuilder: (_, index) {
            late Widget item;

            // Cabeçalho
            if (index == 0) {
              item = const Row(
                children: [
                  Expanded(child: Text('Compras', style: AppTheme.bold12White, textAlign: TextAlign.center)),
                  _verticalDivider,
                  Expanded(child: Text('Vendas', style: AppTheme.bold12White, textAlign: TextAlign.center)),
                ],
              );

              // Corpo
            } else {
              final itemIndex = index - 1;
              item = Row(
                children: [
                  Expanded(
                    child: Text(
                      itemIndex < buyList.length ? buyList[itemIndex].title ?? '' : '',
                      style: AppTheme.regular12White,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  _verticalDivider,
                  Expanded(
                    child: Text(
                      itemIndex < sellList.length ? sellList[itemIndex].title ?? '' : '',
                      style: AppTheme.regular12White,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              );
            }

            // Item final
            return SizedBox(height: 48.0, child: item);
          },
        ),
      ),
    );
  }
}
