import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/partner/partner_trade_unhired_history_page_controller.dart';
import 'partner_trade_list_page.dart';
import '../trade_unhired_history_page.dart';

class PartnerTradeUnhiredHistoryPage extends StatelessWidget {
  const PartnerTradeUnhiredHistoryPage({super.key});

  static const routeName = '${PartnerTradeListPage.routeName}/recomendacoes';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: PartnerTradeUnhiredHistoryPageController(),
      builder: (controller) {
        return TradeUnhiredHistoryPage(controller: controller);
      },
    );
  }
}
