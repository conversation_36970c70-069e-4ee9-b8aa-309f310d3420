import 'partner_trade_list_page.dart';
import '../trade_how_it_works_page.dart';

class PartnerTradeHowItWorksPage extends TradeHowItWorksPage {
  const PartnerTradeHowItWorksPage({super.key});

  static const routeName = '${PartnerTradeListPage.routeName}/como-funciona';

  @override
  String get bodyText =>
      'Trade do Parceiro é um serviço que te ajuda a realizar operações de renda variável automaticamente, com base nas recomendações de parceiros selecionados. Ao ativar um parceiro, sempre que ele(a), emitir uma recomendação de operação, realizaremos a parte operacional para você, simples assim. Confira as condições de contratação de cada parceiro clicando em “Perfil”.'
      '\n\nVocê também pode acessar a relatórios de recomendações e informações adicionais de cada parceiro ao tocar no ícone de vídeo sobre a imagem do parceiro';

  @override
  String? get bmfDecoratedText =>
      '{Atenção:} Ao ativar o Trade do Parceiro BMF, você não poderá alocar limites para operações manuais no mercado futuro. Caso deseje operar contratos como WIN e WDO manualmente, além das operações automatizadas pelo Trade do Parceiro BMF, será necessário entrar em contato com a nossa equipe de atendimento para ajustes.';
}
