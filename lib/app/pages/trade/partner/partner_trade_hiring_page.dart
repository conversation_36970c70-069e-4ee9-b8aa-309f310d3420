import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/partner/partner_trade_hiring_page_controller.dart';
import 'partner_trade_list_page.dart';
import '../trade_hiring_page.dart';

class PartnerTradeHiringPage extends StatelessWidget {
  const PartnerTradeHiringPage({super.key});

  static const routeName = '${PartnerTradeListPage.routeName}/contratacao';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: PartnerTradeHiringPageController(),
      builder: (controller) {
        return TradeHiringPage(controller: controller);
      },
    );
  }
}
