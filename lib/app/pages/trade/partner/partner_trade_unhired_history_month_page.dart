import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/partner/partner_trade_unhired_history_month_page_controller.dart';
import 'partner_trade_unhired_history_page.dart';
import '../trade_unhired_history_month_page.dart';

class PartnerTradeUnhiredHistoryMonthPage extends StatelessWidget {
  const PartnerTradeUnhiredHistoryMonthPage({super.key});

  static const routeName = '${PartnerTradeUnhiredHistoryPage.routeName}/mes';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: PartnerTradeUnhiredHistoryMonthPageController(),
      builder: (controller) {
        return TradeUnhiredHistoryMonthPage(
          controller: controller,
        );
      },
    );
  }
}
