import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/partner/partner_trade_hired_history_page_controller.dart';
import 'partner_trade_list_page.dart';
import '../trade_hired_history_page.dart';

class PartnerTradeHiredHistoryPage extends StatelessWidget {
  const PartnerTradeHiredHistoryPage({super.key});

  static const routeName = '${PartnerTradeListPage.routeName}/operacoes';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: PartnerTradeHiredHistoryPageController(),
      builder: (controller) {
        return TradeHiredHistoryPage(controller: controller);
      },
    );
  }
}
