import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/partner/partner_trade_hired_history_month_page_controller.dart';
import 'partner_trade_hired_history_page.dart';
import '../trade_hired_history_month_page.dart';

class PartnerTradeHiredHistoryMonthPage extends StatelessWidget {
  const PartnerTradeHiredHistoryMonthPage({super.key});

  static const routeName = '${PartnerTradeHiredHistoryPage.routeName}/encerradas/mes';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: PartnerTradeHiredHistoryMonthPageController(),
      builder: (controller) {
        return TradeHiredHistoryMonthPage(
          controller: controller,
        );
      },
    );
  }
}
