import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/partner/partner_trade_list_page_controller.dart';
import '../../tabs/home-menu/my_investments_page.dart';
import 'partner_trade_list_page.dart';
import '../partner/partner_trade_how_to_hire_page.dart';
import '../trade_my_list_page.dart';

class PartnerTradeMyListPage extends StatelessWidget {
  const PartnerTradeMyListPage({super.key});

  static const routeName = '${MyInvestmentsPage.routeName}${PartnerTradeListPage.routeName}';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: PartnerTradeListPageController(),
      builder: (controller) {
        return TradeMyListPage(
          controller: controller,
          pageTitle: 'Trade do Parceiro',
          infoPageRouteName: PartnerTradeHowToHirePage.routeName,
          pageSubtitle: 'Parceiros Contratados',
          emptyListTitleText: 'Você ainda não tem nenhum parceiro contratado.',
          emptyListBodyText:
              'Trade do Parceiro é um serviço que te ajuda a realizar operações de renda variável automaticamente, com base nas recomendações de parceiros selecionados. Clique no botão abaixo para conhecer os parceiros disponíveis.',
          emptyListButtonText: 'Contratar Parceiros',
          listPageRouteName: PartnerTradeListPage.routeName,
        );
      },
    );
  }
}
