import 'package:get/get.dart';

import '../../../controllers/trade/partner/partner_trade_list_page_controller.dart';
import 'partner_trade_list_page.dart';
import 'partner_trade_my_list_page.dart';
import '../trade_how_to_hire_page.dart';

class PartnerTradeHowToHirePage extends TradeHowToHirePage {
  const PartnerTradeHowToHirePage({super.key});

  static const routeName = '${PartnerTradeMyListPage.routeName}/como-contratar';

  @override
  String get title => 'Como contratar parceiros?';

  @override
  String get bodyText =>
      'Trade do Parceiro é um serviço que te ajuda a realizar operações de renda variável automaticamente, com base nas recomendações de parceiros selecionados. Clique no botão abaixo para conhecer os parceiros disponíveis.';

  @override
  String get buttonText => 'Parceiros Disponíveis';

  @override
  void onButtonPressed() {
    Get.find<PartnerTradeListPageController>().tabController.animateTo(0);
    Get.toNamed(PartnerTradeListPage.routeName);
  }
}
