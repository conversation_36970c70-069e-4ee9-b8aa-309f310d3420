import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../models/trade/trade_position.dart';
import '../../utils/extensions.dart';
import '../../widgets/card/trade_structured_operation_item_card.dart';
import '../../widgets/info/horizontal_field.dart';
import '../../widgets/list/app_scrollbar.dart';
import '../../widgets/scaffold/app_scaffold.dart';

abstract class TradeHiredHistoryOpenPositionDetailPage extends StatelessWidget {
  const TradeHiredHistoryOpenPositionDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    final TradePosition? position = Get.arguments;

    return AppScaffold(
      appBar: const TopBar(title: 'Detalhes'),
      extendBody: true,
      body: CMBody(
        child: AppScrollbar(
          child: SingleChildScrollView(
            padding: AppTheme.pagePadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Informações
                HorizontalField('Operação', position?.operationTitle ?? '', padding: const EdgeInsets.only(bottom: 8)),
                const HorizontalField('Status', 'Em aberto', padding: EdgeInsets.only(bottom: 8)),
                HorizontalField('Data de início', position?.operationStartDate?.toddMMyyyy() ?? '', padding: const EdgeInsets.only(bottom: 8)),
                HorizontalField('Ratio de Entrada', position?.entry?.asPtBrDecimal ?? '', padding: const EdgeInsets.only(bottom: 8)),
                HorizontalField('Gain', position?.operationGain?.asPtBrDecimal ?? '', padding: const EdgeInsets.only(bottom: 8)),
                HorizontalField('Loss', position?.operationLoss?.asPtBrDecimal ?? '', padding: const EdgeInsets.only(bottom: 8)),
                HorizontalField('Valor inicial', position?.initialAmount?.asCurrency() ?? '', padding: const EdgeInsets.only(bottom: 24)),

                // Lista
                const Text('Movimentações', style: AppTheme.regular14White),
                const SizedBox(height: 8),
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  separatorBuilder: (_, __) => const SizedBox(height: 16),
                  itemCount: (position?.operationMovements ?? []).length,
                  itemBuilder: (_, index) {
                    final movement = position!.operationMovements![index];
                    return TradeStructuredOperationItemCard(
                      ticker: movement.title ?? '',
                      quantity: movement.quantity?.asPtBrDecimal ?? '',
                      averagePrice: movement.averagePrice?.asCurrency() ?? '',
                      operationType: movement.movementType ?? '',
                      result: movement.financialValue?.toMaskedAmount(showPlusSign: true) ?? '',
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
