import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../controllers/trade/trade_unhired_history_month_page_controller.dart';
import '../../utils/extensions.dart';
import '../../widgets/buttons/trade_history_bottom_bar_hiring_button.dart';
import '../../widgets/card/trade_history_spot_futures_card.dart';
import '../../widgets/card/trade_history_structured_operation_card.dart';
import '../../widgets/info/gain_loss_chip.dart';
import '../../widgets/list/trade_fixed_header_list.dart';
import '../../widgets/scaffold/app_scaffold.dart';

class TradeUnhiredHistoryMonthPage extends StatelessWidget {
  final TradeUnhiredHistoryMonthPageController controller;

  const TradeUnhiredHistoryMonthPage({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: const TopBar(title: 'Histórico de Operações'),

      // Botão de ativação
      bottomNavigationBar: controller.trade != null
          ? TradeHistoryBottomBarHiringButton(
              trade: controller.trade!,
              hiringRouteName: controller.hiringRouteName,
            )
          : null,

      // Conteúdo
      body: CMBody(
        onGetData: controller.loadMonthHistory,
        errorText: 'Não foi possível carregar as operações do mês.',
        ignoreEmptyState: true,
        child: TradeFixedHeaderList(
          fullWidthLabel: controller.tradeLabel,
          fullWidthValue: controller.trade?.fullName ?? '',
          topLeftLabel: 'Mês',
          topLeftValue: controller.selectedMonth.monthDate.toMonthAndYear(),
          topRightLabel: 'Resultado do mês',
          topRightValue: GainLossChip(
            controller.monthHistory?.recommendationsMonthResult ?? 0,
            isPoints: controller.trade?.isBmf == true,
          ),
          totalItems: controller.monthHistory?.recommendations.length ?? 0,
          itemBuilder: (_, index) {
            final operation = controller.monthHistory!.recommendations[index];
            if (controller.trade?.isStructuredOperation == true) {
              return TradeHistoryStructuredOperationCard(
                operation: operation,
                onDetailsPressed: () => Get.toNamed(controller.monthDetailRouteName, arguments: operation),
              );
            } else {
              return TradeHistorySpotFuturesCard(
                ticker: operation.ticker,
                positionType: operation.positionType,
                isBmf: controller.trade?.isBmf == true,
                performance: operation.recommendationResultPercentage,
                startDate: operation.recommendationStartDate,
                endDate: operation.endDate,
                isHired: controller.trade?.isHired == true,
                entryAmount: operation.entry,
                exitAmount: operation.recommendationExitPrice,
                numberOfContracts: operation.numberOfContracts,
              );
            }
          },
          listBottomInformation: Text(controller.informationText, style: AppTheme.regular12White),
        ),
      ),
    );
  }
}
