import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/analyst/analyst_trade_hired_history_page_controller.dart';
import 'analyst_trade_list_page.dart';
import '../trade_hired_history_page.dart';

class AnalystTradeHiredHistoryPage extends StatelessWidget {
  const AnalystTradeHiredHistoryPage({super.key});

  static const routeName = '${AnalystTradeListPage.routeName}/operacoes';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: AnalystTradeHiredHistoryPageController(),
      builder: (controller) {
        return TradeHiredHistoryPage(controller: controller);
      },
    );
  }
}
