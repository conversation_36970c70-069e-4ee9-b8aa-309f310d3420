import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/analyst/analyst_trade_hired_history_month_page_controller.dart';
import 'analyst_trade_hired_history_page.dart';
import '../trade_hired_history_month_page.dart';

class AnalystTradeHiredHistoryMonthPage extends StatelessWidget {
  const AnalystTradeHiredHistoryMonthPage({super.key});

  static const routeName = '${AnalystTradeHiredHistoryPage.routeName}/encerradas/mes';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: AnalystTradeHiredHistoryMonthPageController(),
      builder: (controller) {
        return TradeHiredHistoryMonthPage(
          controller: controller,
        );
      },
    );
  }
}
