import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/analyst/analyst_trade_list_page_controller.dart';
import '../../tabs/home-menu/my_investments_page.dart';
import 'analyst_trade_list_page.dart';
import 'analyst_trade_how_to_hire_page.dart';
import '../trade_my_list_page.dart';

class AnalystTradeMyListPage extends StatelessWidget {
  const AnalystTradeMyListPage({super.key});

  static const routeName = '${MyInvestmentsPage.routeName}${AnalystTradeListPage.routeName}';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: AnalystTradeListPageController(),
      builder: (controller) {
        return TradeMyListPage(
          controller: controller,
          pageTitle: 'Trade do Analista',
          infoPageRouteName: AnalystTradeHowToHirePage.routeName,
          pageSubtitle: 'Analistas Contratados',
          emptyListTitleText: 'Você ainda não tem nenhum analista contratado.',
          emptyListBodyText:
              'Trade do Analista é um serviço que te ajuda a realizar operações de renda variável automaticamente, com base nas recomendações de analistas selecionados. Clique no botão abaixo para conhecer os analistas disponíveis.',
          emptyListButtonText: 'Contratar Analistas',
          listPageRouteName: AnalystTradeListPage.routeName,
        );
      },
    );
  }
}
