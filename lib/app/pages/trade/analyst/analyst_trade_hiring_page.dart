import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/analyst/analyst_trade_hiring_page_controller.dart';
import 'analyst_trade_list_page.dart';
import '../trade_hiring_page.dart';

class AnalystTradeHiringPage extends StatelessWidget {
  const AnalystTradeHiringPage({super.key});

  static const routeName = '${AnalystTradeListPage.routeName}/contratacao';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: AnalystTradeHiringPageController(),
      builder: (controller) {
        return TradeHiringPage(controller: controller);
      },
    );
  }
}
