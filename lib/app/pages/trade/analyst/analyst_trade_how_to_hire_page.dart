import 'package:get/get.dart';

import '../../../controllers/trade/analyst/analyst_trade_list_page_controller.dart';
import 'analyst_trade_list_page.dart';
import 'analyst_trade_my_list_page.dart';
import '../trade_how_to_hire_page.dart';

class AnalystTradeHowToHirePage extends TradeHowToHirePage {
  const AnalystTradeHowToHirePage({super.key});

  static const routeName = '${AnalystTradeMyListPage.routeName}/como-contratar';

  @override
  String get title => 'Como contratar analistas?';

  @override
  String get bodyText =>
      'Trade do Analista é um serviço que te ajuda a realizar operações de renda variável automaticamente, com base nas recomendações de analistas selecionados. Clique no botão abaixo para conhecer os analistas disponíveis.';

  @override
  String get buttonText => 'Analistas Disponíveis';

  @override
  void onButtonPressed() {
    Get.find<AnalystTradeListPageController>().tabController.animateTo(0);
    Get.toNamed(AnalystTradeListPage.routeName);
  }
}
