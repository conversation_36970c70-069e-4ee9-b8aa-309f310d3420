import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/analyst/analyst_trade_unhired_history_month_page_controller.dart';
import 'analyst_trade_unhired_history_page.dart';
import '../trade_unhired_history_month_page.dart';

class AnalystTradeUnhiredHistoryMonthPage extends StatelessWidget {
  const AnalystTradeUnhiredHistoryMonthPage({super.key});

  static const routeName = '${AnalystTradeUnhiredHistoryPage.routeName}/mes';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: AnalystTradeUnhiredHistoryMonthPageController(),
      builder: (controller) {
        return TradeUnhiredHistoryMonthPage(
          controller: controller,
        );
      },
    );
  }
}
