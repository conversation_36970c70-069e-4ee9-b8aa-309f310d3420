import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/trade/analyst/analyst_trade_unhired_history_page_controller.dart';
import 'analyst_trade_list_page.dart';
import '../trade_unhired_history_page.dart';

class AnalystTradeUnhiredHistoryPage extends StatelessWidget {
  const AnalystTradeUnhiredHistoryPage({super.key});

  static const routeName = '${AnalystTradeListPage.routeName}/recomendacoes';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: AnalystTradeUnhiredHistoryPageController(),
      builder: (controller) {
        return TradeUnhiredHistoryPage(controller: controller);
      },
    );
  }
}
