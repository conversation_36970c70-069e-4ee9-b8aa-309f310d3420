import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../controllers/trade/trade_list_page_controller.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/buttons/info_button.dart';
import '../../widgets/card/side_border_card.dart';
import '../../widgets/loading/pull_to_refresh.dart';
import '../../widgets/loading/skeleton.dart';
import '../../widgets/scaffold/app_scaffold.dart';
import '../../widgets/card/trade_list_card.dart';

class TradeMyListPage extends StatelessWidget {
  final TradeListPageController controller;
  final String pageTitle;
  final String infoPageRouteName;
  final String pageSubtitle;
  final String emptyListTitleText;
  final String emptyListBodyText;
  final String emptyListButtonText;
  final String listPageRouteName;

  const TradeMyListPage({
    super.key,
    required this.controller,
    required this.pageTitle,
    required this.infoPageRouteName,
    required this.pageSubtitle,
    required this.emptyListTitleText,
    required this.emptyListBodyText,
    required this.emptyListButtonText,
    required this.listPageRouteName,
  });

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: TopBar(
        title: pageTitle,
        icon: InfoButton(
          onPressed: () {
            Get.toNamed(infoPageRouteName);
          },
        ),
      ),
      extendBody: true,
      body: CMBody(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: AppTheme.pagePadding.copyWith(bottom: AppTheme.pagePadding.top),
              child: Text(
                pageSubtitle,
                style: AppTheme.bold16White,
                textAlign: TextAlign.left,
              ),
            ),
            if (controller.hiredTrades.isNotEmpty || controller.isLoading)
              Expanded(
                child: _List(
                  onRefresh: controller.getTrades,
                  itemCount: controller.hiredTrades.length,
                  isLoading: controller.isLoading,
                  itemBuilder: (_, index) => TradeListCard(
                    trade: controller.hiredTrades[index],
                    controller: controller,
                  ),
                ),
              )
            else
              Padding(
                padding: AppTheme.pagePadding.copyWith(top: 0),
                child: Column(
                  children: [
                    SideBorderCard(
                      margin: const EdgeInsets.only(bottom: 20),
                      borderColor: AppTheme.yellowColor2,
                      child: Text(emptyListTitleText),
                    ),
                    Text(emptyListBodyText),
                    Button.elevated(
                      text: emptyListButtonText,
                      margin: const EdgeInsets.only(top: 20),
                      onPressed: () {
                        controller.tabController.animateTo(0);
                        Get.toNamed(listPageRouteName);
                      },
                    ),
                  ],
                ),
              )
          ],
        ),
      ),
    );
  }
}

class _List extends StatelessWidget {
  final int itemCount;
  final Widget? Function(BuildContext, int) itemBuilder;
  final Future<void> Function() onRefresh;
  final bool isLoading;

  const _List({required this.itemCount, required this.itemBuilder, required this.onRefresh, this.isLoading = false});

  @override
  Widget build(BuildContext context) {
    return PullToRefresh(
      onRefresh: onRefresh,
      child: ListView.separated(
        padding: AppTheme.pagePadding.copyWith(top: 0),
        separatorBuilder: (_, __) => const SizedBox(height: 24),
        itemCount: isLoading ? 5 : itemCount,
        itemBuilder: isLoading ? (_, __) => Skeleton(height: 230, borderRadius: BorderRadius.circular(16)) : itemBuilder,
      ),
    );
  }
}
