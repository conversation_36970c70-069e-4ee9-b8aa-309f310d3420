import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controllers/trade/trade_hired_history_page_controller.dart';
import '../../models/trade/trade_month_performance.dart';
import '../../models/trade/trade_position.dart';
import '../../utils/extensions.dart';
import '../../widgets/buttons/trade_history_bottom_bar_hiring_button.dart';
import '../../widgets/card/trade_open_position_spot_futures_card.dart';
import '../../widgets/card/trade_open_position_structured_operation_card.dart';
import '../../widgets/info/trade_history_header.dart';
import '../../widgets/list/month_amount_list_item.dart';
import '../../widgets/list/trade_hired_history_page_list.dart';
import '../../widgets/scaffold/app_scaffold.dart';
import '../../widgets/tabs/tab_bar.dart';
import '../../widgets/tabs/tabs.dart';

class TradeHiredHistoryPage extends StatelessWidget {
  final TradeHiredHistoryPageController controller;

  const TradeHiredHistoryPage({super.key, required this.controller});

  Future<void> Function() get _onRefresh => controller.bodyKey.currentState?.onChanged ?? () async {};
  List<TradePosition> get _openPositions => controller.openPositions?.openPositions ?? [];
  List<TradeMonthPerformance> get _monthsWithClosedPositions => controller.monthsWithClosedPositions;

  TradeHistoryHeader get _header => TradeHistoryHeader(
        name: controller.trade.fullName,
        shortDescription: controller.trade.shortDescription,
        rows: [
          TradeHistoryHeaderRow(
            controller.performanceLabel,
            controller.trade.performance,
            chipMask: controller.trade.isBmf ? AmountMask.points : AmountMask.percentage,
          ),
        ],
      );

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: const TopBar(title: 'Histórico de Operações'),

      // Editar
      bottomNavigationBar: TradeHistoryBottomBarHiringButton(
        trade: controller.trade,
        hiringRouteName: controller.editingRouteName,
      ),

      // Conteúdo
      body: CMBody(
        key: controller.bodyKey,
        onGetData: controller.loadData,
        errorText: 'Não foi possível carregar o histórico.',
        ignoreEmptyState: true,
        child: Tabs(
          tabs: const [Tab(text: 'Operações em aberto'), Tab(text: 'Operações encerradas')],
          tabsDecoration: CMTabBar.outlinedTabBarDecoration.copyWith(indicatorSize: TabBarIndicatorSize.tab),
          children: [
            // Operações em aberto
            TradeHiredHistoryPageList(
              header: _header,
              onRefresh: _onRefresh,
              totalItems: _openPositions.length,
              emptyText: 'As próximas recomendações operadas aparecerão aqui!',
              itemBuilder: (_, index) {
                final position = _openPositions[index];
                // Operações estruturadas
                if (controller.trade.isStructuredOperation) {
                  return TradeOpenPositionStructuredOperationCard(
                    position: position,
                    onDetailsPressed: () => Get.toNamed(controller.openPositionDetailRouteName, arguments: position),
                  );
                  // Operações mercado à vista e futuro
                } else {
                  return TradeOpenPositionSpotFuturesCard(
                    position: position,
                    isBmf: controller.trade.isBmf,
                  );
                }
              },
            ),

            // Operações encerradas
            TradeHiredHistoryPageList(
              header: _header,
              onRefresh: _onRefresh,
              totalItems: _monthsWithClosedPositions.length,
              emptyText: 'As operações encerradas aparecerão aqui!',
              itemBuilder: (_, index) {
                final monthAmountItem = _monthsWithClosedPositions[index];
                return MonthAmountListItem(
                  onPressed: () => controller.onClosedPositionsMonthPressed(monthAmountItem),
                  month: monthAmountItem.monthDate,
                  amountLabel: controller.closedPositionsMonthPerformanceLabel,
                  amount: monthAmountItem.performance ?? 0,
                  amountMask: controller.closedPositionsMonthPerformanceMask,
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
