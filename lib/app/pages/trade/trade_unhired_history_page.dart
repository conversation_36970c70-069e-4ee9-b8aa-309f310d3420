import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../controllers/trade/trade_unhired_history_page_controller.dart';
import '../../utils/extensions.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/buttons/trade_history_bottom_bar_hiring_button.dart';
import '../../widgets/info/trade_history_header.dart';
import '../../widgets/list/month_amount_list_item.dart';
import '../../widgets/list/app_scrollbar.dart';
import '../../widgets/scaffold/app_scaffold.dart';

class TradeUnhiredHistoryPage extends StatelessWidget {
  final TradeUnhiredHistoryPageController controller;

  const TradeUnhiredHistoryPage({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: const TopBar(title: 'Histórico de Operações'),

      // Botão de ativação
      bottomNavigationBar: TradeHistoryBottomBarHiringButton(
        trade: controller.trade,
        hiringRouteName: controller.hiringRouteName,
      ),

      // Conteúdo
      body: CMBody(
        onGetData: controller.getLastYearHistory,
        errorText: 'Não foi possível carregar o histórico.',
        ignoreEmptyState: true,
        child: AppScrollbar(
          child: SingleChildScrollView(
            padding: AppTheme.pagePadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Cabeçalho
                TradeHistoryHeader(
                  name: controller.trade.fullName,
                  shortDescription: controller.trade.shortDescription,
                  rows: [
                    TradeHistoryHeaderRow(
                      'Resultado bruto dos últimos 12 meses',
                      controller.trade.performance,
                      chipMask: controller.trade.isBmf ? AmountMask.points : AmountMask.percentage,
                    ),
                    TradeHistoryHeaderRow('Taxa de acerto', controller.trade.successRate),
                    TradeHistoryHeaderRow('Média por recomendação', controller.trade.recommendationAverage),
                  ],
                ),

                // Botão para perfil
                Button.outlined(
                  margin: const EdgeInsets.symmetric(vertical: 16),
                  leftIcon: Icons.info_outline,
                  text: controller.profileButtonLabel,
                  onPressed: () {
                    Get.toNamed(controller.profileRouteName, arguments: controller.trade);
                  },
                ),

                // Lista de meses
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  separatorBuilder: (_, __) => const SizedBox(height: 12),
                  itemCount: controller.tradeLastYearHistory.length,
                  itemBuilder: (_, index) {
                    final monthAmountItem = controller.tradeLastYearHistory[index];
                    return MonthAmountListItem(
                      onPressed: () => controller.onMonthPressed(monthAmountItem),
                      month: monthAmountItem.monthDate,
                      amountLabel: 'Resultado do mês',
                      amount: monthAmountItem.performance ?? 0,
                      amountMask: controller.trade.isBmf ? AmountMask.points : AmountMask.percentage,
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
