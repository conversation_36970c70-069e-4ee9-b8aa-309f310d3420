import 'package:flutter/material.dart';

import '../../config/constants.dart';
import '../../config/app_theme.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/card/side_border_card.dart';
import '../../widgets/info/decorated_text.dart';
import '../../widgets/scaffold/app_scaffold.dart';

abstract class TradeHowToHirePage extends StatelessWidget {
  const TradeHowToHirePage({super.key});

  String get title;
  String get bodyText;
  String get buttonText;
  void onButtonPressed();

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: TopBar(title: title),
      extendBody: true,
      body: CMBody(
        child: ListView(
          padding: AppTheme.pagePadding,
          children: [
            Text(bodyText, style: AppTheme.regular14White),
            Button.elevated(
              text: buttonText,
              margin: const EdgeInsets.only(top: 20),
              onPressed: onButtonPressed,
            ),
            const SideBorderCard(
              margin: EdgeInsets.only(top: 20),
              borderColor: AppTheme.greenColor3,
              child: DecoratedText(
                Constants.tradeBalanceWarningDecoratedText,
                decoratedTextStyle: AppTheme.bold14White,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
