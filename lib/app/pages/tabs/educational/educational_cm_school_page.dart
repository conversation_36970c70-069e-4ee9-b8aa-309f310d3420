import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/educational/educational_cm_school_page_controller.dart';
import '../../../widgets/card/video_card.dart';
import '../../../widgets/scaffold/app_scaffold.dart';

class EducationalCmSchoolPage extends StatelessWidget {
  static const routeName = '/educacional/cm-school';

  const EducationalCmSchoolPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: const TopBar(
        title: 'CM School',
        // TODO: Revisar funcionalidade de histórico
        // icon: HistoryButton(EducationalCmSchoolHistoryPage.routeName),
      ),
      extendBody: true,
      body: GetX(
        init: EducationalCmSchoolPageController(),
        builder: (controller) {
          // final courses = controller.courses;
          return CMBody(
            // key: controller.bodyKey,
            // onGetData: controller.fetchBalanceAndCourses,
            onGetData: controller.fetchVideos,
            child: SingleChildScrollView(
              padding: EdgeInsets.fromLTRB(20, 20, 20, 20 + Get.mediaQuery.viewPadding.bottom),
              //   child: Column(
              //     crossAxisAlignment: CrossAxisAlignment.start,
              //     children: [
              //       if (courses.isNotEmpty)
              //         ListView.separated(
              //           physics: const NeverScrollableScrollPhysics(),
              //           scrollDirection: Axis.vertical,
              //           shrinkWrap: true,
              //           separatorBuilder: (_, __) => const SizedBox(height: 30),
              //           itemCount: courses.length,
              //           itemBuilder: (_, index) {
              //             final course = courses[index];

              //             // Card do curso
              //             return EducationalCard(
              //               constraints: BoxConstraints(maxHeight: course.isEnrolled ? 420 : 370),
              //               id: course.id,
              //               title: course.name,
              //               cost: course.price == 0 ? 'Grátis' : course.price?.asCurrency(),
              //               imageUrl: "${Uri.parse(Constants.apiUrl).origin}/${course.imageUrl}",
              //               actionText: course.isEnrolled ? 'Matriculado' : 'Fazer matrícula',
              //               contentWidget: Column(
              //                 children: [
              //                   // Descrição
              //                   SizedBox(
              //                     height: 90,
              //                     child: Text(
              //                       '${course.shortDescription}\n\n${course.longDescription}',
              //                       style: AppTheme.regular12Black,
              //                       overflow: TextOverflow.fade,
              //                     ),
              //                   ),
              //                   const SizedBox(height: 15),

              //                   // Botão para acessar curso, quando matriculado
              //                   if (course.isEnrolled)
              //                     Button.outlined(
              //                       text: 'Acessar Curso',
              //                       textStyle: AppTheme.medium14Orange,
              //                       onPressed: controller.goToCourse,
              //                       height: 40,
              //                     ),
              //                 ],
              //               ),
              //               onAction: course.isEnrolled ? null : () => controller.enroll(course),
              //             );
              //           },
              //         )
              //       else
              //         DatalessStateWidget.empty(text: 'Não há cursos para exibir.'),
              //     ],
              //   ),
              // ),

              // Lista de vídeos de playlist específica será mostrada temporariamente. Por isso código anterior foi comentado.
              child: ListView.separated(
                physics: const BouncingScrollPhysics(),
                cacheExtent: 320,
                separatorBuilder: (_, __) => const SizedBox(height: 20),
                itemCount: controller.videos.length,
                shrinkWrap: true,
                itemBuilder: (_, index) {
                  return VideoCard(
                    controller.videos[index],
                    constraints: const BoxConstraints.tightFor(height: 320),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }
}
