import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../config/app_theme.dart';
import '../../../controllers/educational/educational_cm_school_history_page_controller.dart';
import '../../../utils/extensions.dart';
import '../../../widgets/scaffold/top_bar.dart';
import '../../../widgets/info/badge.dart';
import '../../../widgets/body/cm_body.dart';
import '../../../widgets/scaffold/app_scaffold.dart';
import '../../../widgets/card/educational_history_card.dart';

class EducationalCmSchoolHistoryPage extends StatelessWidget {
  static const routeName = '/educacional/cm-school/historico';

  const EducationalCmSchoolHistoryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetX(
      init: EducationalCmSchoolHistoryPageController(),
      builder: (controller) {
        return AppScaffold(
          appBar: TopBar(
            title: 'Histórico CM School',
            icon: 'assets/icons/action_filter_orange.svg',
            iconOnPressed: controller.filterByPeriod,
          ),
          extendBody: true,
          body: CMBody(
            key: controller.bodyKey,
            onGetData: controller.fetchHistory,
            loadingText: 'Carregando histórico...',
            errorText: 'Não foi possível carregar seu histórico de cursos.',
            errorButtonText: 'Voltar',
            onErrorButtonPressed: () => Navigator.of(context).pop(),
            emptyText: 'Não há cursos para exibir neste período.',
            emptyButtonText: 'Alterar período',
            onEmptyButtonPressed: controller.filterByPeriod,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 15),
                Wrap(
                  children: [
                    const SizedBox(width: 15),
                    CMBadge(
                      text: 'Data inicial: ${controller.dateFilters.value.dataInicio?.toddMMyyyy() ?? ''}',
                      margin: EdgeInsets.zero,
                      backgroundColor: AppTheme.blueColor,
                    ),
                    const SizedBox(width: 10),
                    CMBadge(
                      text: 'Data final: ${controller.dateFilters.value.dataFim?.toddMMyyyy() ?? ''}',
                      margin: EdgeInsets.zero,
                      backgroundColor: AppTheme.blueColor,
                    ),
                  ],
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: ListView.builder(
                      padding: EdgeInsets.only(bottom: Get.mediaQuery.viewPadding.bottom),
                      shrinkWrap: true,
                      physics: const BouncingScrollPhysics(),
                      itemCount: controller.courses.length,
                      itemBuilder: (_, index) {
                        final course = controller.courses[index];
                        return EducationalHistoryCard(
                          id: course.id,
                          name: course.name ?? '',
                          dateStart: course.startDate?.toddMMyyyy() ?? "-",
                          dateEnd: course.endDate?.toddMMyyyy() ?? "-",
                          amount: course.price == 0 ? "Grátis" : (course.price?.asCurrency() ?? "-"),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
