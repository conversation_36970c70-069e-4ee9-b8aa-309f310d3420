import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../config/app_theme.dart';
import '../../../controllers/educational/educational_traders_coach_page_controller.dart';
import '../../../widgets/card/expandable_button_card.dart';
import '../../../widgets/card/video_card.dart';
import '../../../widgets/input/dropdown_search.dart';
import '../../../widgets/scaffold/app_scaffold.dart';

class EducationalTradersCoachPage extends StatelessWidget {
  static const routeName = '/educacional/coach-do-trader';

  const EducationalTradersCoachPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: const TopBar(title: 'Coach do Trader'),
      extendBody: true,
      body: GetX(
          init: EducationalTradersCoachPageController(),
          builder: (controller) {
            return CMBody(
              onGetData: controller.fetchVideos,
              child: SingleChildScrollView(
                padding: EdgeInsets.only(bottom: Get.mediaQuery.viewPadding.bottom),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Título
                    const Padding(
                      padding: EdgeInsets.all(25),
                      child: Text(
                        'Domine a mente e multiplique seus lucros!',
                        style: AppTheme.semi18White,
                      ),
                    ),

                    // Card expansível
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      child: ExpandableButtonCard(
                        title: 'Cissa Grilli',
                        titleMargin: const EdgeInsets.fromLTRB(13, 0, 13, 10),
                        description: 'Tudo o que você precisa saber para se tornar um coach profissional',
                        contentMargin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                        firstButtonText: controller.showCardBody.isFalse ? 'Ver mais' : 'Ver menos',
                        firstButtonOutline: false,
                        buttonHeight: 54,
                        showBody: controller.showCardBody.value,
                        firstButtonOnPressed: () => controller.showCardBody.value = !controller.showCardBody.value,
                        body: Column(
                          children: [
                            // Texto informativo
                            const Padding(
                              padding: EdgeInsets.symmetric(vertical: 20.0),
                              child: Text(
                                'Todos nós possuímos crenças, valores e modelos mentais que adquirimos desde que nascemos. '
                                'As pessoas iniciam no mercado financeiro acreditando ser algo extremamente técnico, mecânico e '
                                'complexo, quando na verdade é analise de um comportamento repetitivo. Essas crenças, na '
                                'grande maioria das vezes, negativas, impedem o progresso e sucesso das pessoas dentro do '
                                'mercado financeiro.'
                                '\n\n'
                                'Mais de 80% das pessoas que eu treinei dentro do mercado financeiro, não cometiam erros '
                                'técnicos. Possuíam sim diversas crenças negativas e uma mente programada de forma errada, ou '
                                'seja, não tinham consciência que faziam isso.',
                                style: AppTheme.regular14White,
                              ),
                            ),

                            // Vídeo
                            if (controller.expandableCardVideo.value != null)
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: 20),
                                child: VideoThumbnail(controller.expandableCardVideo.value),
                              ),
                          ],
                        ),
                      ),
                    ),

                    // Dropdown
                    DropdownSearch<int>(
                      listItemDisplay: (year) => year.toString(),
                      initialItem: controller.selectedYear.value,
                      onItemSelected: (year) => controller.selectedYear.value = year,
                      onSearch: (_) async => controller.availableYears.toList(),
                      isSearchable: false,
                      padding: const EdgeInsets.only(left: 25, right: 20, bottom: 10),
                      textFieldStyle: AppTheme.bold14Orange,
                      textFieldBorder: InputBorder.none,
                      listTextStyle: AppTheme.bold14Orange,
                      suffixIcon: const Icon(Icons.arrow_drop_down, color: AppTheme.orangeColor),
                      emptyText: 'Não há vídeos disponíveis.',
                    ),

                    // Lista de vídeos
                    if (controller.selectedYearVideos.isNotEmpty)
                      SizedBox(
                        height: 320,
                        child: ListView.separated(
                          scrollDirection: Axis.horizontal,
                          itemCount: controller.selectedYearVideos.length,
                          shrinkWrap: true,
                          physics: const BouncingScrollPhysics(),
                          separatorBuilder: (_, __) => const SizedBox(width: 20),
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          itemBuilder: (_, index) => VideoCard(
                            controller.selectedYearVideos[index],
                            constraints: const BoxConstraints.tightFor(width: 256, height: 288),
                          ),
                        ),
                      )
                    else
                      DatalessStateWidget.empty(text: 'Não há vídeos para exibir aqui.'),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            );
          }),
    );
  }
}
