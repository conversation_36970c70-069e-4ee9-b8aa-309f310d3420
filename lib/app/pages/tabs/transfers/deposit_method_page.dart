import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../config/app_theme.dart';
import '../../../controllers/transfers/deposit_method_page_controller.dart';
import '../../../utils/extensions.dart';
import '../../../widgets/buttons/button.dart';
import '../../../widgets/card/pix_key_card.dart';
import '../../../widgets/card/side_border_card_with_decorated_text.dart';
import '../../../widgets/card/user_card.dart';
import '../../../widgets/info/decorated_text.dart';
import '../../../widgets/loading/skeleton.dart';
import '../../../widgets/media/youtube_player.dart';
import '../../../widgets/modal/pix_modals.dart.dart';
import '../../../widgets/scaffold/app_scaffold.dart';
import '../../../widgets/tabs/tab_bar.dart';
import '../../../widgets/tabs/tabs.dart';
import 'deposit_information_page.dart';

class DepositMethodPage extends StatelessWidget {
  const DepositMethodPage({super.key});

  static const routeName = '/transferencias/depositos/metodos';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: DepositMethodPageController(),
      builder: (controller) {
        return CMYouTubePlayer.controller(
          // Chave gerada a cada build para carregar corretamente o vídeo definido.
          key: UniqueKey(),
          controller: controller.youtubeController,
          builder: (_, player) {
            // Define qual vídeo deve ser exibido de acordo com a aba selecionada. O vídeo da outra aba fica com um `Skeleton` no lugar. Caso não haja vídeo, nada é mostrado.
            final pixPlayer = controller.tabController.index == 0
                    ? player
                    : const Skeleton(height: 200);
            final tedPlayer = controller.tabController.index == 1
                    ? player
                    : const Skeleton(height: 200);

            return AppScaffold(
              appBar: TopBar(
                title: 'Depósito',
                icon: Padding(
                  padding: const EdgeInsets.only(right: 10),
                  child: IconButton(
                    icon: const Icon(Icons.help_outline),
                    onPressed: () => Get.toNamedAndPopExistent(DepositInformationPage.routeName),
                  ),
                ),
              ),
              extendBody: true,
              body: CMBody(
                child: Tabs(
                  controller: controller.tabController,
                  tabsDecoration: CMTabBar.outlinedTabBarDecoration.copyWith(indicatorSize: TabBarIndicatorSize.tab),
                  tabs: const [
                    Tab(text: 'Pix'),
                    Tab(text: 'TED'),
                  ],
                  children: [
                    _Method(player: pixPlayer),
                    _Method(player: tedPlayer, isTed: true),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}

class _Method extends StatelessWidget {
  final Widget? player;
  final bool isTed;

  const _Method({this.player, this.isTed = false});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Informação sobre horário
          DecoratedText(
            isTed
                ? 'Realize um TED para sua conta CM Capital utilizando as informações abaixo! Depósitos com TED serão efetivados em dias úteis entre {6:30h e 17:00h}.'
                : 'Transfira o valor desejado para a chave pix abaixo para realizar o depósito. Depósitos com Pix serão efetivados em dias úteis entre {6:30h e 23:59h}.',
            textStyle: AppTheme.regular14White,
            decoratedTextStyle: AppTheme.bold14White,
          ),

          // Dados para depósito
          if (isTed)
            const UserCard(
              margin: EdgeInsets.only(top: 24),
              showCopyIcon: true,
              showColorIcon: false,
              backgroundColor: AppTheme.blueColor6,
              boxShadow: AppTheme.boxShadowBlack,
            )
          else ...[
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 24),
              child: PixKeyCard(),
            ),
            Button.outlined(
              text: 'Copiar chave Pix',
              leftIcon: Icons.copy_rounded,
              onPressed: PixModals.copyPixKey,
            ),
          ],

          // Aviso de mesma titularidade
          depositSameHolderWarning,

          // Aviso de única chave Pix
          if (!isTed)
            const SideBorderCardWithDecoratedText(
              borderColor: AppTheme.yellowColor2,
              decoratedTextStyle: AppTheme.bold14White,
              decoratedText: '{Fique atento:} Essa é a única chave Pix oficial da CM Capital. Representantes da CM Capital não solicitarão transferências para outras chaves.',
            ),

          // Aviso de notificação
          depositNotificationWarning,

          // Vídeo
          if (player != null)
            Padding(
              padding: const EdgeInsets.only(top: 24),
              child: ClipRRect(
                borderRadius: const BorderRadius.all(Radius.circular(10)),
                child: player,
              ),
            ),

          // Ir para tela Depósitos
          Opacity(
            opacity: 0.6,
            child: Button.text(
              margin: const EdgeInsets.only(top: 38, bottom: 24),
              text: 'Informações sobre depósitos',
              leftIcon: Icons.help_outline,
              textStyle: AppTheme.regular16White,
              onPressed: () => Get.toNamedAndPopExistent(DepositInformationPage.routeName),
            ),
          ),
        ],
      ),
    );
  }
}
