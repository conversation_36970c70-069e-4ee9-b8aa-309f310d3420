import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../config/app_theme.dart';
import '../../../controllers/balance_controller.dart';
import '../../../widgets/buttons/button.dart';
import '../../../widgets/buttons/deposit_buttons.dart';
import '../../../widgets/buttons/sensitive_data_button.dart';
import '../../../widgets/card/side_border_card_with_decorated_text.dart';
import '../../../widgets/info/available_balance.dart';
import '../../../widgets/info/decorated_text.dart';
import '../../../widgets/scaffold/app_scaffold.dart';
import '../../help_page.dart';

class DepositInformationPage extends StatelessWidget {
  const DepositInformationPage({super.key});

  static const routeName = '/transferencias/depositos';

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BalanceController>(builder: (controller) {
      return AppScaffold(
        appBar: TopBar(
          title: 'Depós<PERSON><PERSON>',
          icon: const SensitiveDataButton(),
          secondaryIcon: Padding(
            padding: const EdgeInsets.only(right: 10),
            child: IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: controller.loadPatrimony,
            ),
          ),
        ),
        extendBody: true,
        body: CMBody(
          child: SingleChildScrollView(
            padding: AppTheme.pagePadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Saldo
                AvailableBalance(
                  margin: const EdgeInsets.only(bottom: 24),
                  balance: controller.balance.availableBalance,
                  isLoading: controller.isLoadingPatrimony,
                ),

                // Botões Pix e TED
                const DepositButtons(text: 'Deposite para investir!'),

                // Aviso de mesma titularidade
                depositSameHolderWarning,

                // Aviso de favorecido no pix
                depositPixNotificationWarning,

                // Aviso de horário
                const SideBorderCardWithDecoratedText(
                  decoratedText:
                      'Depósitos realizados fora do respectivo horário de funcionamento {serão efetivados na próxima abertura para depósitos} (6:30h do próximo dia útil).',
                ),

                // Aviso de notificação
                depositNotificationWarning,

                // Aviso de atualização de saldo
                const SideBorderCardWithDecoratedText(
                  decoratedText: 'Ainda não está vendo seu depósito no saldo atual? Tente recarregar a página no ícone no topo da página!',
                ),

                // Central de ajuda
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 24),
                  child: DecoratedText(
                    'Em caso de dúvidas, nossos canais de atendimento estão sempre disponíveis, para mais informações confira a {Central de Ajuda}.',
                    textStyle: AppTheme.regular12White,
                    decoratedTextStyle: AppTheme.bold12White,
                  ),
                ),
                Button.outlined(
                  text: 'Central de ajuda',
                  leftIcon: Icons.help_outline,
                  onPressed: () => Get.toNamed(HelpPage.routeName, arguments: true),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
