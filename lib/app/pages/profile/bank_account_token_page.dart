import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../controllers/bank_account_registration_controller.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/scaffold/top_bar.dart';
import '../../widgets/body/cm_body.dart';
import '../../widgets/scaffold/app_scaffold.dart';

class BankAccountTokenPage extends StatelessWidget {
  static const routeName = '/perfil/nova-conta-bancaria/token';

  // TODO: utilizar tela de token quando back estiver atualizado
  const BankAccountTokenPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: const TopBar(title: 'Confirmar conta bancária'),
      body: CMBody(
        child: Center(
          child: SingleChildScrollView(
            padding: AppTheme.pagePadding,
            child: GetX<BankAccountRegistrationController>(
              builder: (controller) {
                return Form(
                  // key: controller.tokenFormKey,
                  // autovalidateMode: controller.tokenFormValidateMode.value,
                  child: Column(
                    children: [
                      // Texto informativo
                      const Text(
                        'Insira aqui o token recebido no seu e-mail de cadastro.',
                        style: AppTheme.regular14White,
                        textAlign: TextAlign.center,
                      ),

                      // Campo para token
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(18.0),
                        margin: const EdgeInsets.symmetric(vertical: 24.0),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8.0),
                          border: Border.all(
                            color: AppTheme.greyColor,
                          ),
                        ),
                        child: const Column(
                          children: [
                            Text(
                              'O token enviado foi:',
                              style: AppTheme.regular14White,
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 20),
                            // PinCodeTextField(
                            //   appContext: context,
                            //   length: 6,
                            //   onChanged: (value) => controller.token.value = value,
                            //   keyboardType: TextInputType.number,
                            //   validator: (value) => validateMinimumLength(value, 6),
                            //   cursorColor: Colors.amber,
                            //   textStyle: AppTheme.bold24White,
                            //   animationType: AnimationType.none,
                            //   inputFormatters: numberInputFormatter,
                            //   pinTheme: PinTheme(
                            //     activeColor: AppTheme.orangeColor,
                            //     selectedColor: AppTheme.orangeColor,
                            //     inactiveColor: AppTheme.greyColor,
                            //   ),
                            // ),
                          ],
                        ),
                      ),

                      // Botão submit
                      Button.elevated(
                        text: 'Verificar',
                        disabled: controller.isCreatingBankAccount.value,
                        onPressed: controller.createBankAccount,
                      ),

                      // Botão para reenviar token
                      // Button.outlined(
                      //   text: controller.resendText,
                      //   disabled: controller.isCreatingBankAccount.value || controller.isCountingDownToResend,
                      //   onPressed: () => controller.requestToken(isTokenPage: true),
                      //   margin: const EdgeInsets.only(top: 12.0),
                      // ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
