import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/info/decorated_text.dart';
import '../../widgets/scaffold/app_scaffold.dart';
import '../../widgets/tabs/tab_bar.dart';
import '../../widgets/tabs/tabs.dart';
import '../help_page.dart';

class ContactDataUpdatePage extends StatelessWidget {
  const ContactDataUpdatePage({super.key});

  static const routeName = '/minha-conta/seguranca/atualizacao-contato';

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: const TopBar(title: 'Atualização de contato'),
      extendBody: true,
      body: CMBody(
        child: Tabs(
          tabsDecoration: CMTabBar.outlinedTabBarDecoration.copyWith(indicatorSize: TabBarIndicatorSize.tab),
          tabs: const [Tab(text: 'Celular'), Tab(text: 'E-mail')],
          children: const [
            // Celular
            SingleChildScrollView(
              padding: AppTheme.pagePadding,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Como atualizar o celular de contato?', style: AppTheme.semi16White),
                  SizedBox(height: 16),
                  DecoratedText(
                    'Para atualizar seu celular de contato envie uma solicitação para o e-mail {<EMAIL>} com o número novo para ser atualizado, esse e-mail {precisa ser enviado do seu e-mail atual de cadastro}.'
                    '\n\nSe não tiver mais acesso ao seu e-mail atual de cadastro, siga as instruções da seção “E-mail” e inclua o número novo de celular com os demais itens requeridos para a atualização de e-mail.',
                    textStyle: AppTheme.regular14White,
                    decoratedTextStyle: AppTheme.bold14White,
                  ),
                  _HelpButton(),
                ],
              ),
            ),

            // E-mail
            SingleChildScrollView(
              padding: AppTheme.pagePadding,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Como atualizar o e-mail de contato?', style: AppTheme.semi16White),
                  SizedBox(height: 16),
                  _EmailDecoratedTextSection(
                    number: 1,
                    text: 'Se você ainda tem acesso ao e-mail de contato atual, basta enviar uma solicitação a partir do e-mail atual para {<EMAIL>}.'
                        '\n\nEssa solicitação precisa incluir:'
                        '\n • Seu {CPF},'
                        '\n • seu {nome completo},'
                        '\n • o {novo e-mail} que deseja cadastrar.',
                  ),
                  Divider(color: Colors.white24, thickness: 1, height: 80, indent: 12, endIndent: 12),
                  _EmailDecoratedTextSection(
                    number: 2,
                    text: 'Se você {não tem acesso} ao e-mail de contato atual, além dos itens acima, a solicitação por e-mail precisa incluir:'
                        '\n • 1 foto de um {documento} de identificação (RG ou CNH), '
                        '\n • 1 foto no formato {selfie} segurando o documento de identificação,'
                        '\n • 1 foto de uma {carta} preenchida de próprio punho e assinada.',
                  ),
                  SizedBox(height: 32),
                  Text(
                    'Lembrando que todas as fotos devem estar com boa qualidade e a documentação deve estar legível.',
                    style: AppTheme.regular14White,
                  ),
                  _HelpButton(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _EmailDecoratedTextSection extends StatelessWidget {
  final int number;
  final String text;
  const _EmailDecoratedTextSection({required this.number, required this.text});
  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('$number. ', style: AppTheme.regular14White),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: 2),
            child: DecoratedText(
              text,
              textStyle: AppTheme.regular14White,
              decoratedTextStyle: AppTheme.bold14White,
            ),
          ),
        ),
      ],
    );
  }
}

class _HelpButton extends StatelessWidget {
  const _HelpButton();
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 64),
        const Text('Em caso de dúvidas, entre em contato pelos Canais de Atendimento.', style: AppTheme.regular14White),
        const SizedBox(height: 24),
        Button.outlined(
          leftIcon: Icons.help_outline,
          text: 'Canais de atendimento',
          onPressed: () => Get.toNamed(HelpPage.routeName),
        ),
      ],
    );
  }
}
