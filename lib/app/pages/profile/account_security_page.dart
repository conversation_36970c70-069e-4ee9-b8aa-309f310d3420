import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../controllers/profile/account_security_page_controller.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/info/decorated_text.dart';
import '../../widgets/input/input.dart';
import '../../widgets/loading/skeleton.dart';
import '../../widgets/scaffold/app_scaffold.dart';
import '../../widgets/input/cm_switch.dart';
import 'contact_data_update_page.dart';

class AccountSecurityPage extends StatelessWidget {
  const AccountSecurityPage({super.key});

  static const routeName = '/minha-conta/seguranca';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: AccountSecurityPageController(),
      builder: (controller) {
        return AppScaffold(
          appBar: const TopBar(title: '<PERSON><PERSON><PERSON><PERSON> da conta'),
          extendBody: true,
          body: CMBody(
            child: Padding(
              padding: AppTheme.pagePadding,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Informações da conta
                  const Text('Informações da conta', style: AppTheme.semi16White),
                  const SizedBox(height: 16),
                  DecoratedText(
                    '{${controller.userName}}, garanta a segurança da conta mantendo suas informações de contato atualizadas. Atualmente, suas informações de contato para recebimento de códigos e comunicações de segurança são:',
                    textStyle: AppTheme.regular14White,
                    decoratedTextStyle: AppTheme.bold14White,
                  ),

                  // Carregando informações da conta
                  const SizedBox(height: 32),
                  if (controller.isFetchingUserContactData) ...[
                    const Skeleton(height: 73),
                    const SizedBox(height: 23),
                    const Skeleton(height: 73),

                    // Erro ao carregar informações da conta
                  ] else if (controller.didThrowUserContactDataError) ...[
                    DatalessStateWidget.error(
                      text: 'Houve um erro ao obter as informações da conta',
                      buttonText: 'Tentar novamente',
                      onButtonPressed: controller.fetchUserContactData,
                    ),

                    // Campos de informação
                  ] else ...[
                    Input(
                      label: 'E-mail de contato',
                      initialValue: controller.userEmail,
                      enabled: false,
                      readOnly: true,
                    ),
                    Input(
                      label: 'Celular de contato',
                      initialValue: controller.userPhoneNumber,
                      enabled: false,
                      readOnly: true,
                    ),

                    // Botão para ir à tela de como atualizar informações
                    Button.outlined(
                      margin: const EdgeInsets.only(top: 10),
                      text: 'Como atualizar as informações?',
                      onPressed: () => Get.toNamed(ContactDataUpdatePage.routeName),
                    ),
                  ],

                  // Acesso ao app
                  const Divider(color: Colors.white30, thickness: 1, height: 80),
                  const Text('Acesso ao app', style: AppTheme.semi16White),
                  const SizedBox(height: 29),

                  // Carregando biometria
                  if (controller.isFetchingBiometricsData) ...[
                    const Skeleton(height: 73),

                    // Erro ao carregar biometria
                  ] else if (controller.didThrowBiometricsDataError) ...[
                    DatalessStateWidget.error(
                      text: 'Houve um erro ao obter as informações da biometria',
                      buttonText: 'Tentar novamente',
                      onButtonPressed: controller.fetchBiometricsData,
                    ),

                    // Biometria
                  ] else ...[
                    ListTile(
                      title: const Text('Biometria', style: AppTheme.regular16White),
                      trailing: CMSwitch(
                        isOn: controller.isBiometricsEnabled,
                        onChanged: controller.onBiometricsSwitchChanged,
                      ),
                      contentPadding: EdgeInsets.zero,
                    ),
                    const Divider(color: AppTheme.transparentWhiteColor, thickness: 1),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
