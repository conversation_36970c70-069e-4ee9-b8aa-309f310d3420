import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';

import '../../api/usuario/usuario_api.dart';
import '../../config/app_theme.dart';
import '../../controllers/user_controller.dart';
import '../../errors/error_handlers.dart';
import '../../errors/exceptions.dart';
import '../../utils/extensions.dart';
import '../../utils/get_file.dart';
import '../../utils/local_storage.dart';
import '../../utils/toast.dart';
import '../modal/cm_bottom_sheet.dart';
import '../loading/fullscreen_loading.dart';

class UserAvatar extends StatelessWidget {
  final String? userName;
  final VoidCallback? onPressed;
  final bool showEditButton;
  final EdgeInsets padding;
  final double diameter;

  static const maximumFileSizeInBytes = 5 * 1024 * 1024;

  static UserController get _userController => Get.find<UserController>();
  Rxn<File> get avatar => _userController.avatar;

  UserAvatar({
    super.key,
    String? userName,
    this.onPressed,
    this.showEditButton = false,
    this.padding = EdgeInsets.zero,
    this.diameter = 120,
  }) : userName = userName ?? _userController.usuario?.nome;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Padding(
        padding: padding,
        child: Obx(
          () => Stack(
            alignment: AlignmentDirectional.center,
            clipBehavior: Clip.none,
            children: [
              CircleAvatar(
                backgroundColor: AppTheme.orangeColor,
                radius: diameter / 2,
                foregroundImage: avatar.value != null
                    ? FileImage(
                        avatar.value!,
                      )
                    : null,
                child: userName != null
                    ? Text(
                        userName.initials,
                        style: AppTheme.semi18White.copyWith(fontSize: 50 * diameter / 120),
                        textAlign: TextAlign.center,
                      )
                    : null,
              ),
              if (showEditButton)
                Positioned(
                  bottom: -5,
                  right: -5,
                  child: _ChangeAvatarButton(
                    onPressed: _showAvatarChangeDialog,
                  ),
                )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _showAvatarChangeDialog() async {
    if (avatar.value == null) {
      await CMBottomSheet.choice(
        description: 'Deseja carregar uma foto de perfil?',
        topButtonText: 'Sim',
        topButtonOnPressed: () {
          Get.back();
          _changeAvatar();
        },
        bottomButtonText: 'Não',
        bottomButtonOnPressed: Get.back,
      ).show();
    } else {
      await CMBottomSheet.choice(
        topButtonText: 'Carregar nova foto de perfil',
        topButtonOnPressed: () {
          Get.back();
          _changeAvatar();
        },
        bottomButtonText: 'Remover foto',
        bottomButtonOnPressed: () async {
          await _deleteRemoteAndLocalAvatars();
          Get.back();
        },
      ).show();
    }
  }

  Future<void> _changeAvatar() async {
    final usuarioApi = Get.find<UsuarioApi>();
    final storage = LocalStorage();

    try {
      final result = await FilePicker.platform.pickFiles(type: FileType.image, withData: true);
      if (result == null) return;

      final cacheFile = result.files.first;

      if (cacheFile.bytes == null || cacheFile.path == null) {
        Toast.error(message: 'Houve um erro ao carregar sua foto.').show();
        return;
      }

      showFullscreenLoading();

      File? file = await getFileFromBytes(cacheFile.bytes!, cacheFile.name);
      File(cacheFile.path!).deleteSync();

      file = await cropImage(file);
      if (file == null) return Get.back();

      ensureFileDoesntExceedMaximumSize(file);

      await usuarioApi.cadastrarAvatarUsuario(file);

      if (avatar.value?.existsSync() ?? false) avatar.value?.deleteSync();
      avatar.value = file;

      await storage.write(LocalStorageKeys.avatar, file.path);
      Get.back();
    } on MaximumFileSizeExceededException catch (error) {
      Get.back();
      await CMBottomSheet.simple(
        description: 'O tamanho da imagem depois de cortada não pode exceder ${maximumFileSizeInBytes.toMegaBytes()}.\n\n'
            'Sua imagem tem ${error.size.toMegaBytes()}',
      ).show();
    } on PlatformException {
      Get.back();
      await CMBottomSheet.simple(
        description: 'Houve um problema com a foto escolhida.\n\nTente escolher outra foto.',
      ).show();
    } catch (error) {
      Get.back();
      await onError(error);
    }
  }

  Future<void> _deleteRemoteAndLocalAvatars() async {
    try {
      await Get.find<UsuarioApi>().deletarAvatarUsuario();

      _userController.deleteAvatarLocalCopy();
    } catch (error) {
      await onError(error);
    }
  }

  Future<File?> cropImage(File image) async {
    final croppedImage = await ImageCropper().cropImage(
      sourcePath: image.path,
      aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
      cropStyle: CropStyle.circle,
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Recorte a imagem',
          showCropGrid: false,
          cropFrameColor: Colors.transparent,
          toolbarColor: AppTheme.blueColor,
          toolbarWidgetColor: AppTheme.whiteColor,
          activeControlsWidgetColor: AppTheme.orangeColor,
        ),
        IOSUiSettings(
          title: 'Recorte a imagem',
          doneButtonTitle: 'Concluído',
          cancelButtonTitle: 'Cancelar',
          aspectRatioLockEnabled: true,
          resetButtonHidden: true,
          aspectRatioPickerButtonHidden: true,
        ),
      ],
    );

    if (croppedImage == null) return null;

    return File(croppedImage.path);
  }

  void ensureFileDoesntExceedMaximumSize(File file) {
    final bytes = file.readAsBytesSync();

    if (bytes.length > maximumFileSizeInBytes) {
      throw MaximumFileSizeExceededException(bytes.length);
    }
  }
}

class _ChangeAvatarButton extends StatelessWidget {
  final VoidCallback onPressed;

  const _ChangeAvatarButton({
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.all(10),
        decoration: const BoxDecoration(
          color: Color(0xFFF5F5F5),
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.add_a_photo_outlined,
          color: AppTheme.blueColor4,
          size: 16,
        ),
      ),
    );
  }
}
