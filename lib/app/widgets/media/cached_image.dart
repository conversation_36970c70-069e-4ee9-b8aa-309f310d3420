import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

import '../loading/skeleton.dart';

/// Configuração do cache para limitar tempo em que imagens são armazenadas no dispositivo.
///
/// Por padrão isso não é definido em `CachedNetworkImage`, então as imagens poderiam ficar armazenadas por tempo indeterminado. Com isso, uma alteração de imagem no servidor, mantendo a mesma URL, não atualizaria a respectiva imagem.
final _cacheManager = CacheManager(Config('networkImagesCacheKey', stalePeriod: const Duration(minutes: 30)));

class CachedImage extends StatelessWidget {
  final String imageUrl;
  final Widget placeholder;
  final Widget errorWidget;
  final BoxFit? fit;
  final double? width;
  final double? height;

  /// Imagem obtida a partir de uma URL e armazenada em cache para evitar downloads desnecessários.
  ///
  /// As imagens ficam armazenadas por 30 minutos.
  const CachedImage({
    super.key,
    required this.imageUrl,
    this.placeholder = const Skeleton(),
    this.errorWidget = const Icon(Icons.error),
    this.fit,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      placeholder: (_, __) => placeholder,
      errorWidget: (_, __, ___) => errorWidget,
      fit: fit,
      width: width,
      height: height,
      cacheManager: _cacheManager,
    );
  }
}
