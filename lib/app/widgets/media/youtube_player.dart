import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class CMYouTubePlayer extends StatefulWidget {
  final String videoCode;
  final Widget Function(BuildContext context, Widget player) builder;
  final Widget? thumbnail;
  final double aspectRatio;
  final bool showVideoProgressIndicator;
  final bool showFullScreenButton;
  final bool hideControls;
  final bool autoPlay;
  final bool mute;
  final bool loop;
  final VoidCallback? onEnterFullScreen;
  final VoidCallback? onExitFullScreen;
  final YoutubePlayerController? controller;

  /// Componente genérico para reproduzir vídeos do YouTube.
  ///
  /// Para que a opção de tela cheia funcione, [builder] deve retornar o [Scaffold] da tela que contém o reprodutor do vídeo.
  const CMYouTubePlayer({
    super.key,
    required this.videoCode,
    required this.builder,
    this.thumbnail,
    this.aspectRatio = 16 / 9,
    this.showVideoProgressIndicator = false,
    this.showFullScreenButton = true,
    this.hideControls = false,
    this.autoPlay = false,
    this.mute = false,
    this.loop = false,
    this.onEnterFullScreen,
    this.onExitFullScreen,
  }) : controller = null;

  CMYouTubePlayer.controller({
    super.key,
    required YoutubePlayerController this.controller,
    required this.builder,
    this.thumbnail,
    this.aspectRatio = 16 / 9,
    this.showVideoProgressIndicator = false,
    this.showFullScreenButton = true,
    this.onEnterFullScreen,
    this.onExitFullScreen,
  })  : videoCode = controller.initialVideoId,
        hideControls = controller.flags.hideControls,
        autoPlay = controller.flags.autoPlay,
        mute = controller.flags.mute,
        loop = controller.flags.loop;

  @override
  State<CMYouTubePlayer> createState() => _CMYouTubePlayerState();
}

class _CMYouTubePlayerState extends State<CMYouTubePlayer> {
  late final _videoController = widget.controller ??
      YoutubePlayerController(
        initialVideoId: widget.videoCode,
        flags: YoutubePlayerFlags(
          hideControls: widget.hideControls,
          autoPlay: widget.autoPlay,
          mute: widget.mute,
          loop: widget.loop,
        ),
      );

  @override
  void initState() {
    SystemChrome.setPreferredOrientations(DeviceOrientation.values);

    super.initState();
  }

  void resetSystemUIMode() {
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.manual,
      overlays: SystemUiOverlay.values,
    );
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    resetSystemUIMode();

    _videoController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return YoutubePlayerBuilder(
      player: YoutubePlayer(
        controller: _videoController,
        thumbnail: widget.thumbnail,
        aspectRatio: widget.aspectRatio,
        showVideoProgressIndicator: widget.showVideoProgressIndicator,
        bottomActions: widget.showFullScreenButton
            ? null
            : [
                CurrentPosition(),
                const SizedBox(width: 8.0),
                ProgressBar(
                  isExpanded: true,
                ),
                RemainingDuration(),
                const PlaybackSpeedButton(),
              ],
      ),
      builder: widget.builder,
      onEnterFullScreen: widget.onEnterFullScreen,
      onExitFullScreen: () {
        resetSystemUIMode();
        widget.onExitFullScreen?.call();
      },
    );
  }
}
