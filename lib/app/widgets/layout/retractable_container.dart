import 'package:flutter/material.dart';

import '../../config/app_theme.dart';

class RetractableContainer extends StatefulWidget {
  final Widget child;
  final double minHeight;
  final EdgeInsets padding;
  final bool allowRetraction;
  final bool useShader;
  final bool showText;
  final Widget Function(bool)? customButtonBuilder;

  const RetractableContainer({
    super.key,
    required this.child,
    this.minHeight = 200.0,
    this.padding = EdgeInsets.zero,
    this.allowRetraction = true,
    this.useShader = true,
    this.showText = true,
    this.customButtonBuilder,
  });

  @override
  State<RetractableContainer> createState() => _RetractableContainerState();
}

class _RetractableContainerState extends State<RetractableContainer> {
  bool isRetracted = true;

  final childKey = GlobalKey();
  double maxHeight = 0;

  /// Agenda a execução para o próximo frame para garantir que [childKey] sempre tenha um currentContext e
  /// [maxHeight] possa ser calculado corretamente.
  void _scheduleMaxHeightCalculation() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (maxHeight != 0) return;

      final childRenderBox = childKey.currentContext?.findRenderObject() as RenderBox?;

      final calculatedMaxHeight = childRenderBox?.size.height ?? 0;

      if (calculatedMaxHeight != 0) {
        setState(() => maxHeight = calculatedMaxHeight);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.padding,
      child: Column(
        children: [
          ShaderMask(
            shaderCallback: (Rect rect) => const LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                AppTheme.blackColor,
              ],
              stops: [0.6, 1.0],
            ).createShader(rect),
            blendMode: isRetracted && widget.allowRetraction && widget.useShader ? BlendMode.dstOut : BlendMode.dst,
            child: TweenAnimationBuilder(
              tween: Tween<double>(begin: 0, end: widget.allowRetraction && isRetracted ? widget.minHeight : maxHeight),
              curve: Curves.easeInOut,
              duration: const Duration(milliseconds: 200),
              builder: (_, height, child) {
                _scheduleMaxHeightCalculation();

                return SizedBox(
                  height: height,
                  // O SingleChildScrollView sem rolagem é necessário para ocultar o restante do conteúdo quando o container estiver retraído.
                  child: SingleChildScrollView(
                    physics: const NeverScrollableScrollPhysics(),
                    child: child,
                  ),
                );
              },
              child: SizedBox(
                key: childKey,
                child: widget.child,
              ),
            ),
          ),
          const SizedBox(height: 5),
          if (widget.allowRetraction)
            GestureDetector(
              onTap: () {
                setState(() => isRetracted = !isRetracted);
              },
              child: widget.customButtonBuilder?.call(isRetracted) ??
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AnimatedRotation(
                        turns: isRetracted ? 0 : 0.5,
                        duration: const Duration(milliseconds: 200),
                        child: const Icon(
                          Icons.keyboard_arrow_down,
                          size: 30,
                          color: AppTheme.orangeColor,
                        ),
                      ),
                      if (widget.showText) Text(isRetracted ? 'VER MAIS' : 'VER MENOS', style: AppTheme.regular12Orange),
                    ],
                  ),
            ),
        ],
      ),
    );
  }
}
