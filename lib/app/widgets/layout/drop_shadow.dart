import 'dart:ui';

import 'package:flutter/material.dart';

class DropShadow extends StatelessWidget {
  final Widget child;
  final double opacity;
  final Color color;
  final double sigma;

  const DropShadow({
    super.key,
    required this.child,
    this.opacity = 0.5,
    this.color = Colors.grey,
    this.sigma = 2,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        ColorFiltered(
          colorFilter: ColorFilter.mode(
            color.withOpacity(opacity),
            BlendMode.srcIn,
          ),
          child: ImageFiltered(
            imageFilter: ImageFilter.blur(
              sigmaX: sigma,
              sigmaY: sigma,
            ),
            child: child,
          ),
        ),
        child,
      ],
    );
  }
}
