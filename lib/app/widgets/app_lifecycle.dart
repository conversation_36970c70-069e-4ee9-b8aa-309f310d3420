import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../config/app_theme.dart';
import '../utils/extensions.dart';

const _viewBlockerPageRouteName = 'view-blocker';

class AppLifecycle extends StatefulWidget {
  final Widget child;

  const AppLifecycle({super.key, required this.child});

  @override
  State<AppLifecycle> createState() => _AppLifecycleState();
}

class _AppLifecycleState extends State<AppLifecycle> {
  late final AppLifecycleListener _lifecycleListener;

  @override
  void initState() {
    super.initState();
    _lifecycleListener = AppLifecycleListener(
      onInactive: _showOverlay,
      onResume: _hideOverlay,
    );
  }

  void _showOverlay() {
    showGeneralDialog(
        context: Get.context!,
        pageBuilder: (_, __, ___) => const Scaffold(backgroundColor: AppTheme.blueColor),
        transitionDuration: Duration.zero,
        useRootNavigator: false,
        routeSettings: const RouteSettings(name: _viewBlockerPageRouteName));
  }

  void _hideOverlay() {
    Get.popNamedExistent(_viewBlockerPageRouteName);
  }

  @override
  void dispose() {
    _lifecycleListener.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
