import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../api/cm_request.dart';
import '../../config/app_theme.dart';
import '../../controllers/user_controller.dart';
import '../../utils/pending_information.dart';
import '../loading/fullscreen_loading.dart';
import '../scaffold/top_bar.dart';

class AppWebView extends StatefulWidget {
  final String? title;
  final String url;
  final WebViewController? controller;

  const AppWebView(this.url, {this.title, this.controller});

  @override
  State<AppWebView> createState() => _AppWebViewState();
}

class _AppWebViewState extends State<AppWebView> {
  late final WebViewController _controller;
  String? _title;

  @override
  void initState() {
    super.initState();
    _title = widget.title;
    _initializeWebview();
  }

  void _initializeWebview() {
    _controller = widget.controller ?? WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (_) async {
            if (widget.title == null) {
              final title = await _controller.getTitle();
              setState(() => _title = title);
            }
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TopBar(title: _title),
      backgroundColor: AppTheme.blueColor,
      body: SafeArea(
        child: WebViewWidget(controller: _controller),
      ),
    );
  }
}

Future<void> openHomeBrokerWebView() async {
  showFullscreenLoading();

  final hasPendingInformation = await checkPendingInformationForInvesting();
  if (hasPendingInformation) return Get.back();

  final request = Get.find<CMRequest>();
  final userController = Get.find<UserController>();

  final url = 'https://hb.cmcapital.com.br/hbnet2/stintegracaoportalcm/integracaoportalcm.aspx?codigo=${userController.sinacor}&token=${request.token}&app=s';

  // Substitui User-Agent de iPad para evitar problema no layout
  final webviewController = WebViewController();
  var userAgent = await webviewController.getUserAgent();
  if (userAgent case String agent) {
    userAgent = agent.replaceAll('iPad', 'iPhone');
    webviewController.setUserAgent(userAgent);
  }

  Get.off(() => AppWebView(url, title: 'Home Broker', controller: webviewController));
}
