import 'package:flutter/material.dart';

import '../../config/app_theme.dart';

class BorderInfo extends StatelessWidget {
  const BorderInfo({
    this.margin = EdgeInsets.zero,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    this.borderColor = defaultBorderColor,
    this.borderRadius = 5,
    this.backgroundColor,
    required this.child,
  });

  static const defaultBorderColor = AppTheme.blueColor5;

  final EdgeInsets margin;
  final EdgeInsets padding;
  final Color borderColor;
  final double borderRadius;
  final Color? backgroundColor;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      padding: padding,
      decoration: BoxDecoration(
        border: Border.all(color: borderColor),
        borderRadius: BorderRadius.circular(borderRadius),
        color: backgroundColor,
      ),
      child: child,
    );
  }
}
