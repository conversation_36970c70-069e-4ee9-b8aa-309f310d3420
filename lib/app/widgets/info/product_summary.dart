import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import 'vertical_field.dart';

const valuesWithWidthFactor = [
  'Fundo',
  'Emissor',
  'Titulo',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  'Valor solicitado de resgate',
  'Valor disponível para resgate',
  'Quantidade solicitada de resgate',
  'Valor do Investimento',
  'Preço unitário',
  'Descrição',
  'Mensalidade',
  'Plataforma',
  'Duração do investimento',
];

class ProductSummary extends StatelessWidget {
  final String? product;
  final List<VerticalField> fields;
  final bool showTitle;
  final bool isListItem;

  const ProductSummary({
    super.key,
    this.product,
    required this.fields,
    this.showTitle = true,
    this.isListItem = false,
  });

  /// Converte entradas do [Map] em uma lista de [VerticalField].
  ProductSummary.fromMap({
    super.key,
    this.product,
    required Map<String, dynamic> fields,
    this.showTitle = true,
    this.isListItem = false,
  }) : fields = fields.entries.mapIndexed(
          (index, entry) {
            final double widthFactor = valuesWithWidthFactor.contains(entry.key) || index == 0 ? 1 : 0.5;

            return VerticalField(
              entry.key,
              entry.value,
              widthFactor: widthFactor,
              isListItem: isListItem,
            );
          },
        ).toList();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) ...[
          const SizedBox(height: 30),
          const Text('Resumo da solicitação', style: AppTheme.semi12White),
          const SizedBox(height: 10),
        ],
        Container(
          padding: AppTheme.cardPadding,
          width: double.infinity,
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.white.withOpacity(0.4),
              width: 0.8,
            ),
            borderRadius: BorderRadius.circular(5),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (product?.isNotEmpty == true) ...[
                Text(product!, style: AppTheme.regular12White),
                const SizedBox(height: 10),
              ],
              Theme(
                data: ThemeData(
                  textTheme: TextTheme(
                    labelSmall: AppTheme.regular11Orange.copyWith(letterSpacing: 0.5),
                    bodyLarge: AppTheme.regular12White,
                  ),
                ),
                child: Wrap(
                  children: fields,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }
}
