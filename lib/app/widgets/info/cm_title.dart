import 'package:flutter/cupertino.dart';

import '../../config/app_theme.dart';

/// Widget genérico para incluir um título (obrigatório) e um subtítulo (opcional).
class CMTitle extends StatelessWidget {
  final String title;
  final String? subtitle;

  const CMTitle(this.title, [this.subtitle]);

  Widget get child {
    Widget title = Text(this.title, style: AppTheme.bold20White);

    if ((subtitle ?? '').isEmpty) {
      return title;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [title, Text(subtitle!, style: AppTheme.regular16White)],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 10, 20, 0),
      child: child,
    );
  }
}
