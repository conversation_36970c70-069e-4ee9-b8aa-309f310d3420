import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../pages/profile/qualified_investor_page.dart';
import '../../config/app_theme.dart';
import 'badge.dart';
import '../buttons/button.dart';
import '../card/side_border_card.dart';

class QualifiedInvestorNote extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text("Investidor Qualificado", style: AppTheme.regular14White),
            CMBadge(
              text: "QUALIFICADO",
              backgroundColor: AppTheme.greenColor,
              borderRadius: 4,
            ),
          ],
        ),
        const SizedBox(height: 16),
        SideBorderCard(
          borderColor: AppTheme.greenColor,
          padding: const EdgeInsets.symmetric(vertical: 29, horizontal: 27),
          child: Column(
            children: [
              const Text(
                "<PERSON><PERSON><PERSON><PERSON>, você é um investidor Qualificado.\n\nAgora já é possível investir nos produtos destinados a esse público!",
                style: AppTheme.regular12White,
              ),
              const SizedBox(height: 16),
              Button.outlined(
                text: "SAIBA MAIS",
                onPressed: () => Get.toNamed(QualifiedInvestorPage.routeName),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
