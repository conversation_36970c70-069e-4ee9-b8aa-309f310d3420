import 'package:flutter/material.dart';

import '../../config/app_theme.dart';

class AppTooltip extends StatelessWidget {
  final Widget child;
  final String message;
  final Duration? duration;

  const AppTooltip({
    super.key,
    required this.message,
    required this.child,
    this.duration,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: message,
      preferBelow: false,
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: AppTheme.darkColor.withOpacity(0.9),
        borderRadius: const BorderRadius.all(Radius.circular(8.0)),
      ),
      showDuration: duration ?? const Duration(seconds: 2),
      textStyle: AppTheme.regular14White,
      child: child,
    );
  }
}
