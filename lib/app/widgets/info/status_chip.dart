import 'package:flutter/material.dart';

import '../../api/investments/investments_api.dart';
import '../../config/app_theme.dart';
import 'cm_chip.dart';

class StatusChip extends CMChip {
  StatusChip(
    this.product, {
    super.margin,
    super.padding,
  }) : super(
          color: _getBackgroundColor(product.statusSolicitacao),
          child: Text(
            (product.statusSolicitacao ?? '').toUpperCase(),
            style: AppTheme.bold12White.copyWith(color: _getTextColor(product.statusSolicitacao)),
          ),
        );

  final AvailableProduct product;

  static const greenColor = AppTheme.greenColor3;
  static const yellowColor = AppTheme.yellowColor2;
  static const redColor = Color(0xFFB10000);
  static const whiteColor = Color(0xFFCDE1EC);

  static Color _getBackgroundColor(String? status) {
    switch (status?.toUpperCase()) {
      case 'LIQUIDADO':
      case 'REALIZADO':
      case 'ALOCAÇÃO':
      case 'EXECUTADA':
        return greenColor;

      case 'PENDENTE':
      case 'PENDENTE DE CONFIRMAÇÃO':
      case 'EM LIQUIDAÇÃO':
      case 'EM PROCESSAMENTO':
      case 'EM ANÁLISE':
      case 'ANÁLISE':
      case 'AGUARDANDO LIBERAÇÃO':
        return yellowColor;

      case 'CANCELADO':
      case 'CANCELADA':
      case 'REJEITADO':
        return redColor;

      case 'EM EXECUÇÃO':
      case 'ROLAGEM PENDENTE':
      case 'ROLAGEM EM EXECUÇÃO':
      case 'ENCERRAMENTO PENDENTE':
      default:
        return whiteColor;
    }
  }

  static Color _getTextColor(String? status) {
    final backgroundColor = _getBackgroundColor(status);

    return switch (backgroundColor) {
      greenColor => const Color(0xFF062C4D),
      yellowColor => const Color(0xFF101116),
      redColor => const Color(0xFFF5F5F5),
      _ => AppTheme.blueColor8,
    };
  }
}
