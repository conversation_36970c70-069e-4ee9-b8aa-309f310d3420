import 'package:flutter/material.dart';

class CMChip extends StatelessWidget {
  final Widget child;
  final Color color;
  final EdgeInsets padding;
  final EdgeInsets margin;

  const CMChip({
    super.key,
    required this.child,
    required this.color,
    this.padding = const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
    this.margin = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      padding: padding,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(25),
      ),
      child: child,
    );
  }
}
