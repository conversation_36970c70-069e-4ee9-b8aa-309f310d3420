import 'package:flutter/material.dart';

import '../../api/suitability/suitability_api.dart';
import '../../config/app_theme.dart';

class CMBadge extends StatelessWidget {
  final String text;

  /// Padrão: `AppTheme.badgeText.copyWith(color: _color)`
  final TextStyle? textStyle;
  final TextAlign? textAlign;
  final bool? isTextUpperCase;
  final Color backgroundColor;
  final bool disabled;
  final double borderRadius;
  final EdgeInsets? margin;
  final EdgeInsets? padding;
  final UserSuitabilityProfile? suitabilityProfile;

  const CMBadge({
    super.key,
    required this.text,
    this.textStyle,
    this.textAlign,
    this.isTextUpperCase = false,
    this.backgroundColor = AppTheme.orangeColor,
    this.disabled = false,
    this.borderRadius = 20,
    this.margin = const EdgeInsets.only(left: 20),
    this.padding = const EdgeInsets.fromLTRB(10, 3, 10, 3),
  }) : suitabilityProfile = null;

  @Deprecated('Usar `CMBadge.suitability`')
  CMBadge.suitabilityProfile(
    this.suitabilityProfile, {
    super.key,
    this.textStyle,
    this.textAlign,
    this.isTextUpperCase = false,
    this.borderRadius = 20,
    this.margin = const EdgeInsets.only(left: 20),
    this.padding = const EdgeInsets.fromLTRB(10, 3, 10, 3),
  })  : backgroundColor = suitabilityProfile?.color ?? AppTheme.greyColor,
        text = suitabilityProfile?.text ?? 'Não respondido',
        disabled = false;

  /// Criado para substituir o [CMBadge.suitabilityProfile] acima.
  /// A substituição acontecerá gradativamente, dependendo da solicitação de atualização de telas, por isso o antigo ainda é mantido.
  CMBadge.suitability(
    this.suitabilityProfile, {
    super.key,
    this.margin = EdgeInsets.zero,
  })  : padding = const EdgeInsets.fromLTRB(10, 3, 10, 3),
        backgroundColor = suitabilityProfile?.color ?? UserSuitabilityProfile.unanswered.color,
        text = suitabilityProfile?.text ?? UserSuitabilityProfile.unanswered.text,
        textStyle = AppTheme.bold16White,
        textAlign = TextAlign.center,
        isTextUpperCase = true,
        borderRadius = 8,
        disabled = false;

  Color get _color {
    return backgroundColor.computeLuminance() < 0.5 ? AppTheme.whiteColor : AppTheme.blackColor;
  }

  static const defaultTextStyle = TextStyle(
    fontSize: 10,
    height: 1,
  );

  TextStyle get _defaultEnabledTextStyle => defaultTextStyle.copyWith(color: _color);
  TextStyle get _enabledTextStyle => (textStyle ?? _defaultEnabledTextStyle);
  TextStyle get _disabledTextStyle => _enabledTextStyle.copyWith(color: _color.withOpacity(0.25));

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      padding: padding,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(borderRadius), color: disabled ? backgroundColor.withOpacity(0.25) : backgroundColor),
      child: Text(
        isTextUpperCase == true ? text.toUpperCase() : text,
        style: disabled ? _disabledTextStyle : _enabledTextStyle,
        textAlign: textAlign,
      ),
    );
  }
}
