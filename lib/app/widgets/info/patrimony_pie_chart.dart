import 'package:community_charts_flutter/community_charts_flutter.dart' as charts;
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../controllers/sensitive_data_controller.dart';
import '../../models/patrimony_balance_item.dart';
import '../../pages/tabs/rentability/rentability_distribution.dart';

export '../../models/patrimony_balance_item.dart';

class PatrimonyPieChart extends StatelessWidget {
  final double total;
  final List<PatrimonyBalanceItem> balance;
  final double? height;
  final EdgeInsets margin;

  /// {@macro SensitiveData.enabled}
  final bool isSensitive;

  PatrimonyPieChart({
    super.key,
    required List<PatrimonyBalanceItem> balance,
    this.height,
    this.margin = const EdgeInsets.symmetric(vertical: 20.0),
    this.isSensitive = false,
  })  : total = balance.fold(0, (previousValue, element) => element.value + previousValue),
        balance = balance.where((balanceItem) => balanceItem.value > 0).toList();

  @override
  Widget build(BuildContext context) {
    return GetX<SensitiveDataController>(
      builder: (controller) => Container(
        height: height ?? Get.height * 0.3,
        margin: margin,
        child: charts.PieChart<String>(
          getChartSeries(
            (controller.showSensitiveData.value || !isSensitive) && total != 0 ? balance : emptyBalance,
          ),
          animate: true,
          defaultRenderer: CustomArcRendererConfig(
            noDataColor: charts.ColorUtil.fromDartColor(AppTheme.transparentWhiteColor),
            stroke: charts.ColorUtil.fromDartColor(AppTheme.blueColorCardBg),
            arcWidth: 75,
            strokeWidthPx: 4,
            arcRendererDecorators: [
              charts.ArcLabelDecorator<String>(
                labelPosition: charts.ArcLabelPosition.outside,
                labelPadding: 0,
                showLeaderLines: true,
                leaderLineStyleSpec: charts.ArcLabelLeaderLineStyleSpec(
                  color: charts.ColorUtil.fromDartColor(AppTheme.blueColor),
                  thickness: 1,
                  length: 15,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<charts.Series<PatrimonyBalanceItem, String>> getChartSeries(List<PatrimonyBalanceItem> balance) {
    return [
      charts.Series<PatrimonyBalanceItem, String>(
        id: '',
        data: balance,
        colorFn: (balanceItem, _) => charts.ColorUtil.fromDartColor(balanceItem.color),
        domainFn: (balanceItem, _) => balanceItem.label,
        measureFn: (balanceItem, _) => balanceItem.value,
        labelAccessorFn: (balanceItem, _) => '${balanceItem.percentage.toStringAsFixed(2)}%',
        outsideLabelStyleAccessorFn: (_, __) => const charts.TextStyleSpec(
          fontSize: 12,
          color: charts.Color.white,
        ),
      ),
    ];
  }

  List<PatrimonyBalanceItem> get emptyBalance => [
        PatrimonyBalanceItem(
          color: AppTheme.blueColor.withOpacity(0.3),
          value: 1,
        ),
      ];
}
