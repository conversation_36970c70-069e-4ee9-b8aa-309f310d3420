import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../utils/extensions.dart';
import 'amount_colored_chip.dart';

class TradeHistoryHeader extends StatelessWidget {
  const TradeHistoryHeader({
    super.key,
    required this.name,
    this.shortDescription,
    this.rows = const [],
  });

  final String name;
  final String? shortDescription;
  final List<TradeHistoryHeaderRow> rows;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(name, style: AppTheme.regular14White),
        if ((shortDescription ?? '').isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(shortDescription ?? '', style: AppTheme.regular14White.copyWith(color: Colors.white70)),
        ],
        ...rows,
      ],
    );
  }
}

class TradeHistoryHeaderRow extends StatelessWidget {
  const TradeHistoryHeaderRow(this.label, this.amount, {this.chipMask});

  final String label;
  final double? amount;
  final AmountMask? chipMask;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        children: [
          Expanded(child: Text(label, style: AppTheme.regular12Orange)),
          const SizedBox(width: 4),
          if (chipMask != null)
            AmountColoredChip(
              amount ?? 0,
              fontSize: 12,
              mask: chipMask!,
              showPlusSign: true,
            )
          else
            Text(
              amount.asPercentage(),
              style: AppTheme.bold12White,
            ),
        ],
      ),
    );
  }
}
