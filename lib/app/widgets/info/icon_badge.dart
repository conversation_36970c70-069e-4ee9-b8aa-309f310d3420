import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../utils/ui_utils.dart';

class IconBadge extends StatelessWidget {
  final EdgeInsets margin;
  final Color borderColor;
  final double borderRadius;
  final Color? backgroundColor;
  final EdgeInsets padding;
  final dynamic icon;
  final String text;
  final TextStyle textStyle;

  const IconBadge({
    super.key,
    this.margin = EdgeInsets.zero,
    this.borderColor = AppTheme.whiteColor,
    this.borderRadius = 4,
    this.backgroundColor,
    this.padding = const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    required this.icon,
    required this.text,
    this.textStyle = AppTheme.medium14White,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(
        border: Border.all(width: 1, color: borderColor),
        borderRadius: BorderRadius.circular(borderRadius),
        color: backgroundColor,
      ),
      padding: padding,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          iconify(icon),
          const SizedBox(width: 4),
          Text(text, style: textStyle),
        ],
      ),
    );
  }
}
