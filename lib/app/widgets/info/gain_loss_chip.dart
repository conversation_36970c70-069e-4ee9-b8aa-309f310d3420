import '../../utils/extensions.dart';
import 'amount_colored_chip.dart';

class GainLossChip extends AmountColoredChip {
  const GainLossChip(
    double amount, {
    super.key,
    bool isPoints = false,

    /// Mostra um asterisco ao lado de GAIN ou LOSS.
    bool showAsterisk = false,
  }) : super(
          amount,
          mask: isPoints ? AmountMask.points : AmountMask.percentage,
          showPlusSign: true,
          fontSize: 12,
          prefix: '${amount >= 0 ? 'GAIN' : 'LOSS'}${showAsterisk ? '*' : ''}',
        );
}
