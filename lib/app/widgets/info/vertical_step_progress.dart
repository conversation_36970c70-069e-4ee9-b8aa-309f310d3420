import 'package:flutter/material.dart';

import '../../config/app_theme.dart';

class VerticalStepProgress extends StatelessWidget {
  final List<String> steps;
  final int completedSteps;
  final double stepBottomPadding;

  const VerticalStepProgress({
    super.key,
    this.steps = const [
      'Dados Básicos',
      'Endereço',
      'Campo Profissional',
      'Documentos',
    ],
    required this.completedSteps,
    this.stepBottomPadding = 20,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: List.generate(
        steps.length,
        (index) => _ProgressItem(
          step: index + 1,
          text: steps[index],
          textBottomPadding: stepBottomPadding,
          isComplete: index + 1 <= completedSteps,
          isNextComplete: index + 2 <= completedSteps,
          isLast: index + 1 == steps.length,
        ),
      ),
    );
  }
}

class _ProgressItem extends StatelessWidget {
  final int step;
  final String text;
  final double textBottomPadding;
  final bool isComplete;
  final bool isNextComplete;
  final bool isLast;

  const _ProgressItem({
    required this.step,
    required this.text,
    required this.textBottomPadding,
    this.isComplete = false,
    this.isNextComplete = false,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Stack(
        children: [
          // Linha
          if (!isLast)
            Container(
              width: 3,
              color: isNextComplete ? AppTheme.greenColor : AppTheme.greyColor2,
              margin: const EdgeInsets.only(left: 14.5),
            ),

          // Passo
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Círculo com número
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isComplete ? AppTheme.greenColor3 : AppTheme.greyColor2,
                    width: 2.5,
                  ),
                  color: isComplete ? AppTheme.greenColor3 : const Color(0xFF083459),
                ),
                padding: const EdgeInsets.only(bottom: 2),
                child: Center(
                  child: Text(
                    step.toString(),
                    style: isComplete ? AppTheme.semi16Black : AppTheme.regular16Grey2,
                  ),
                ),
              ),

              // Texto
              const SizedBox(width: 10),
              Flexible(
                child: Padding(
                  padding: EdgeInsets.only(top: 2, bottom: textBottomPadding),
                  child: Text(
                    text,
                    style: isComplete ? AppTheme.bold16Green : AppTheme.regular16Grey2,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
