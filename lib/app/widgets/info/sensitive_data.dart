import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../controllers/sensitive_data_controller.dart';

/// Esconde o conteúdo de algum widget de acordo com o valor de [SensitiveDataController.showSensitiveData].
class SensitiveData extends StatelessWidget {
  final Widget child;
  final Color color;
  final AlignmentGeometry contentAlignment;
  final String placeholder;

  /// {@template SensitiveData.enabled}
  /// Controla se valores serão escondidos de acordo com o valor de [SensitiveDataController.showSensitiveData].
  /// {@endtemplate}
  final bool enabled;

  const SensitiveData({
    super.key,
    required this.child,
    this.color = AppTheme.whiteColor,
    this.contentAlignment = Alignment.centerLeft,
    this.placeholder = '• • • • •',
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: const BoxConstraints(minHeight: 24.0),
      child: GetX<SensitiveDataController>(
        builder: (controller) => AnimatedCrossFade(
          duration: const Duration(milliseconds: 200),
          sizeCurve: Curves.ease,
          crossFadeState: controller.showSensitiveData.value || !enabled ? CrossFadeState.showFirst : CrossFadeState.showSecond,
          alignment: contentAlignment,
          layoutBuilder: (topChild, topChildKey, bottomChild, bottomChildKey) => Stack(
            alignment: contentAlignment,
            children: [topChild, bottomChild],
          ),
          firstChild: child,
          secondChild: Text(
            placeholder,
            style: child is Text && (child as Text).style != null
                ? (child as Text).style
                : AppTheme.defaultText.copyWith(
                    fontSize: 24,
                    letterSpacing: 4,
                    height: 1,
                    color: color,
                  ),
            maxLines: 1,
            overflow: TextOverflow.fade,
          ),
        ),
      ),
    );
  }
}
