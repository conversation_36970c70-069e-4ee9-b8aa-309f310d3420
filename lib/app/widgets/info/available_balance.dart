import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../api/patrimony/patrimony_api.dart';
import '../../config/app_theme.dart';
import '../../errors/error_handlers.dart';
import '../../utils/extensions.dart';
import '../buttons/sensitive_data_button.dart';
import 'sensitive_data.dart';

class AvailableBalance extends StatefulWidget {
  final bool isLoading;
  final double? balance;
  final bool showSensitiveDataButton;
  final bool loadInternally;
  final EdgeInsets? margin;
  final String text;

  const AvailableBalance({
    super.key,
    this.isLoading = false,
    required this.balance,
    this.showSensitiveDataButton = false,
    this.margin = const EdgeInsets.only(bottom: 10),
    this.text = 'Saldo Disponível',
  }) : loadInternally = false;

  /// Carrega saldo disponível na conta internamente.
  const AvailableBalance.loadInternally({
    super.key,
    this.showSensitiveDataButton = false,
    this.margin = const EdgeInsets.only(bottom: 10),
    this.text = 'Saldo Disponível',
  })  : isLoading = false,
        balance = null,
        loadInternally = true;

  @override
  State<AvailableBalance> createState() => AvailableBalanceState();
}

class AvailableBalanceState extends State<AvailableBalance> {
  double? _availableBalance;
  bool _isLoading = true;

  double? get availableBalance => widget.loadInternally ? _availableBalance : widget.balance;
  bool get isLoading => widget.loadInternally ? _isLoading : widget.isLoading;

  /// Obtém saldo disponível na conta.
  Future<void> getBalance() async {
    setState(() => _isLoading = true);
    try {
      final balance = await Get.find<PatrimonyApi>().getInvestorBalance();
      _availableBalance = balance.availableBalance;
    } on DioException catch (error) {
      onError(error);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  void initState() {
    super.initState();
    if (widget.loadInternally) {
      getBalance();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 8),
      margin: widget.margin,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        color: AppTheme.blueColor5.withValues(alpha: 0.5),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.showSensitiveDataButton)
                const SensitiveDataButton(
                  padding: EdgeInsets.only(right: 10),
                ),
              Flexible(
                child: Text(widget.text, style: AppTheme.regular14White),
              ),
            ],
          ),
          Flexible(
            child: SensitiveData(
              contentAlignment: Alignment.centerRight,
              child: Text(
                isLoading
                    ? 'Carregando...'
                    : availableBalance == null
                        ? 'Erro'
                        : availableBalance!.asCurrency(),
                style: AppTheme.bold14White,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
