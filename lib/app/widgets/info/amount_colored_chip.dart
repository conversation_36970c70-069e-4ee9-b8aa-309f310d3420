import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../utils/extensions.dart';

class AmountColoredChip extends StatelessWidget {
  const AmountColoredChip(
    this.amount, {
    super.key,
    this.margin = EdgeInsets.zero,
    this.fontSize = 14,
    this.prefix,
    this.mask = AmountMask.currency,
    this.showAsterisk = false,
    this.showPlusSign = false,
  });

  final EdgeInsets margin;
  final double amount;
  final double fontSize;
  final String? prefix;
  final AmountMask mask;
  final bool showAsterisk;
  final bool showPlusSign;

  String get _text {
    var text = (prefix ?? '').isNotEmpty ? '$prefix ' : '';
    text += amount.toMaskedAmount(mask: mask, showPlusSign: showPlusSign);
    text += showAsterisk ? '*' : '';
    return text;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      padding: const EdgeInsets.symmetric(horizontal: 5),
      decoration: BoxDecoration(
        color: amount >= 0 ? const Color(0xFF00701F) : const Color(0xFF9A0000),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Text(
        _text,
        style: AppTheme.bold14White.copyWith(color: const Color(0xFFF5F5F5), fontSize: fontSize),
      ),
    );
  }
}
