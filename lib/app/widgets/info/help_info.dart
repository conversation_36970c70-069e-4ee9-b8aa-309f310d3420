import 'package:flutter/material.dart';

import '../../config/app_theme.dart';

class HelpInfo extends StatelessWidget {
  final String text;
  final double bottomPadding;

  const HelpInfo(this.text, {super.key, this.bottomPadding = 35});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 12, bottom: bottomPadding),
      child: Row(
        children: [
          const Icon(Icons.help_outline, color: AppTheme.greyColor2, size: 14),
          const SizedBox(width: 5),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(color: AppTheme.greyColor2, fontSize: 14, height: 1),
            ),
          )
        ],
      ),
    );
  }
}
