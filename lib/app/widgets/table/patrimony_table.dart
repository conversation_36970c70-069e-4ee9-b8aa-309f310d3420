import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../models/patrimony_balance_item.dart';
import '../../utils/extensions.dart';
import '../info/sensitive_data.dart';

export '../../models/patrimony_balance_item.dart';

class PatrimonyTable extends StatelessWidget {
  final double total;
  final List<PatrimonyBalanceItem> balance;
  final bool showSumRow;
  final borderColor = const Color(0xFF013157);

  /// {@macro SensitiveData.enabled}
  final bool isSensitive;

  PatrimonyTable({
    super.key,
    required this.balance,
    this.showSumRow = false,
    this.isSensitive = false,
  }) : total = balance.fold(0, (previousValue, element) => element.value + previousValue);

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.all(Radius.circular(10.0)),
      child: Table(
        children: [
          tableHeader,
          ...balance.map(
            (item) => TableRow(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: borderColor),
                ),
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.all(10),
                  child: GestureDetector(
                    onTap: item.onTap,
                    child: Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4.0),
                            color: item.color,
                          ),
                          height: 15,
                          width: 15,
                          margin: const EdgeInsets.only(right: 10),
                        ),
                        Flexible(child: Text(item.label, style: AppTheme.regular14White)),
                      ],
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: item.onTap,
                  child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: SensitiveData(
                      enabled: isSensitive,
                      child: Text(
                        item.value.asCurrency(),
                        style: AppTheme.semi14White,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (showSumRow) tableSumRow,
        ],
      ),
    );
  }

  TableRow get tableHeader => const TableRow(
        children: [
          Padding(
            padding: EdgeInsets.all(10.0),
            child: Text('Ativo', style: AppTheme.regular14White),
          ),
          Padding(
            padding: EdgeInsets.all(10.0),
            child: Text('Valor', style: AppTheme.regular14White),
          ),
        ],
      );

  TableRow get tableSumRow => TableRow(
        decoration: BoxDecoration(
          border: Border(
            top: BorderSide(color: borderColor),
          ),
        ),
        children: [
          const Padding(
            padding: EdgeInsets.all(10.0),
            child: Text('Total', style: AppTheme.regular14White),
          ),
          Padding(
            padding: const EdgeInsets.all(10.0),
            child: SensitiveData(
              enabled: isSensitive,
              child: Text(total.asCurrency(), style: AppTheme.regular14White),
            ),
          ),
        ],
      );
}
