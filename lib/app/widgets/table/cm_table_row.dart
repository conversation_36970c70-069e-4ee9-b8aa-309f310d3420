import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../utils/extensions.dart';

class CMTableRow extends StatelessWidget {
  final List<CMTableCell> cells;
  final Color color;

  const CMTableRow(
    this.cells, {
    this.color = Colors.transparent,
  });

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        // ignore: unnecessary_cast
        children: (cells as List<Widget>)
            .placeBetween(
              const VerticalDivider(
                thickness: 1,
                width: 0.8,
                color: AppTheme.transparentWhiteColor,
              ),
            )
            .toList(),
      ),
    );
  }
}

class CMTableCell extends StatelessWidget {
  final dynamic cell;
  final EdgeInsets padding;

  const CMTableCell(
    this.cell, {
    this.padding = const EdgeInsets.fromLTRB(12, 6, 12, 12),
  });

  Widget get _cell {
    if (cell.runtimeType == String) {
      return Text(cell, style: AppTheme.regular12White);
    }
    return cell;
  }

  @override
  Widget build(BuildContext context) {
    return Flexible(
      flex: 1,
      child: Container(
        alignment: Alignment.center,
        padding: padding,
        child: _cell,
      ),
    );
  }
}

class CMTable extends StatelessWidget {
  final CMTableRow? header;
  final List<CMTableRow> rows;

  const CMTable({
    this.header,
    required this.rows,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(
          color: AppTheme.whiteColor,
        ),
      ),
      child: Column(
        children: <Widget>[if (header != null) header!, ...rows]
            .placeBetween(
              const Divider(
                height: 0.8,
                color: AppTheme.transparentWhiteColor,
              ),
            )
            .toList(),
      ),
    );
  }
}
