import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../info/app_tooltip.dart';
import '../info/sensitive_data.dart';

class CMDataTable extends StatelessWidget {
  final List<CMDataTableItem> items;
  final TextStyle? startTextStyle;
  final TextStyle? endTextStyle;
  final Color paintedBackgroundColor;

  /// {@macro SensitiveData.enabled}
  final bool isSensitive;

  const CMDataTable({
    super.key,
    required this.items,
    this.startTextStyle,
    this.endTextStyle,
    this.paintedBackgroundColor = AppTheme.blueColor3,
    this.isSensitive = true,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) => GestureDetector(
          onTap: items[index].onTap,
          child: Container(
            decoration: BoxDecoration(
              color: index.isOdd ? Colors.transparent : paintedBackgroundColor,
              borderRadius: index.isEven ? BorderRadiusDirectional.circular(5) : null,
            ),
            padding: const EdgeInsets.all(10),
            child: Wrap(
              alignment: WrapAlignment.spaceBetween,
              crossAxisAlignment: WrapCrossAlignment.center,
              spacing: 10,
              runSpacing: 10,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Visibility(
                      visible: (items[index].tooltipMessage != null),
                      child: AppTooltip(
                        message: items[index].tooltipMessage ?? '',
                        child: const Icon(
                          Icons.info_rounded,
                          color: AppTheme.whiteColor,
                          size: 32,
                        ),
                      ),
                    ),
                    const SizedBox(width: 5),
                    Text(
                      items[index].label,
                      style: startTextStyle ?? AppTheme.regular12White,
                    ),
                  ],
                ),
                SensitiveData(
                  color: AppTheme.whiteColor,
                  contentAlignment: Get.width < 330 ? Alignment.centerLeft : Alignment.centerRight,
                  enabled: isSensitive,
                  child: Text(
                    items[index].value,
                    style: endTextStyle ?? (items[index].value == "R\$ 0,00" ? AppTheme.semi14White : AppTheme.semi14Orange),
                  ),
                ),
              ],
            ),
          ),
        ),
        itemCount: items.length,
        shrinkWrap: true,
      ),
    );
  }
}

class CMDataTableItem {
  final String label;
  final String value;
  final String? tooltipMessage;
  final void Function()? onTap;

  CMDataTableItem({
    required this.label,
    required this.value,
    this.tooltipMessage,
    this.onTap,
  });
}
