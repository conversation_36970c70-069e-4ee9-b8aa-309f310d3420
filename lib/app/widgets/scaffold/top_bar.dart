import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:collection/collection.dart';

import '../../config/app_theme.dart';
import '../../utils/ui_utils.dart';
import '../../utils/extensions.dart';

class TopBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final bool showLogo;
  final dynamic icon;
  final double iconSize;
  final Color iconColor;
  final VoidCallback? iconOnPressed;
  final dynamic secondaryIcon;
  final double secondaryIconSize;
  final Color secondaryIconColor;
  final VoidCallback? secondaryIconOnPressed;
  final dynamic tertiaryIcon;
  final double tertiaryIconSize;
  final Color tertiaryIconColor;
  final VoidCallback? tertiaryIconOnPressed;
  final bool whiteBackground;
  final String? textButtonDescription;
  final VoidCallback? textButtonOnPressed;
  final Widget? leading;
  final double leadingWidth;
  final List<AppBarMenuOption>? menuOptions;
  final PreferredSizeWidget? bottom;

  @override
  final Size preferredSize = const Size.fromHeight(kToolbarHeight);

  const TopBar({
    super.key,
    this.title,
    this.showLogo = false,
    this.icon,
    this.iconSize = 20,
    this.iconColor = AppTheme.orangeColor,
    this.iconOnPressed,
    this.secondaryIcon,
    this.secondaryIconSize = 20,
    this.secondaryIconColor = AppTheme.whiteColor,
    this.secondaryIconOnPressed,
    this.tertiaryIcon,
    this.tertiaryIconSize = 20,
    this.tertiaryIconColor = AppTheme.whiteColor,
    this.tertiaryIconOnPressed,
    this.whiteBackground = false,
    this.textButtonDescription,
    this.textButtonOnPressed,
    this.leading,
    this.leadingWidth = 56.0,
    this.menuOptions,
    this.bottom,
  });

  Widget _logo() {
    return Padding(
      padding: const EdgeInsets.only(right: 10),
      child: SvgPicture.asset(
        'assets/images/logo.svg',
        colorFilter: ColorFilter.mode(whiteBackground ? AppTheme.blueColor : AppTheme.whiteColor, BlendMode.srcIn),
        height: 32,
      ),
    );
  }

  Widget? _title(BuildContext context) {
    if (title?.isEmpty ?? true) {
      if (showLogo) return _logo();

      return null;
    }

    Widget child = Text(
      title!,
      style: whiteBackground ? AppTheme.regular15Black : AppTheme.regular15White,
      overflow: TextOverflow.ellipsis,
    );

    if (leading != null) return child;

    return Container(
      height: kToolbarHeight,
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () => Navigator.maybePop(context),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(5),
              child: Icon(
                Icons.arrow_back_ios,
                color: whiteBackground ? AppTheme.blueColor : AppTheme.whiteColor,
              ),
            ),
            Expanded(child: child)
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      systemOverlayStyle: whiteBackground ? SystemUiOverlayStyle.dark : SystemUiOverlayStyle.light,
      leading: leading,
      leadingWidth: leading != null ? leadingWidth : 0,
      automaticallyImplyLeading: false,
      title: _title(context),
      elevation: 0,
      backgroundColor: Colors.transparent,
      centerTitle: title?.isEmpty ?? true,
      bottom: bottom,
      actions: [
        if (icon is Widget)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 7),
            child: icon,
          )
        else if (icon != null)
          InkWell(
            onTap: iconOnPressed,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 7),
              child: iconify(icon, color: iconColor, size: iconSize),
            ),
          ),
        if (secondaryIcon is Widget)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 7),
            child: secondaryIcon,
          )
        else if (secondaryIcon != null)
          InkWell(
            onTap: secondaryIconOnPressed,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 7),
              child: iconify(secondaryIcon, color: secondaryIconColor, size: secondaryIconSize),
            ),
          ),
        if (tertiaryIcon is Widget)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 7),
            child: tertiaryIcon,
          )
        else if (tertiaryIcon != null)
          InkWell(
            onTap: tertiaryIconOnPressed,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 7),
              child: iconify(tertiaryIcon, color: tertiaryIconColor, size: tertiaryIconSize),
            ),
          ),
        if (menuOptions != null)
          PopupMenuButton(
            itemBuilder: (context) => menuOptions!
                .map<PopupMenuEntry>(
                  (menuOption) => PopupMenuItem(
                    value: menuOption.label,
                    child: Text(menuOption.label),
                  ),
                )
                .placeBetween(const PopupMenuDivider())
                .toList(),
            onSelected: (selectedItemValue) {
              final selectedOption = menuOptions!.firstWhereOrNull((menuOption) => selectedItemValue == menuOption.label);
              if (selectedOption?.action != null) {
                selectedOption!.action();
              }
            },
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.0),
            ),
            child: const Padding(padding: EdgeInsets.symmetric(horizontal: 15), child: Icon(Icons.more_horiz, size: 30, color: AppTheme.whiteColor)),
          ),
        if (textButtonDescription?.isNotEmpty == true)
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: TextButton(
              onPressed: textButtonOnPressed,
              child: Text(
                textButtonDescription!,
                style: AppTheme.semi14Orange,
              ),
            ),
          )
      ],
    );
  }
}

class AppBarMenuOption {
  final String label;
  final Function action;

  AppBarMenuOption({
    required this.label,
    required this.action,
  });
}
