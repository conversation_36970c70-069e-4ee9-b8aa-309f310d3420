import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../input/input.dart';
import '../buttons/button.dart';

class CMBottomSheet {
  final String? title;
  final String? description;
  final Widget? descriptionWidget;
  final CrossAxisAlignment itemsAlignment;
  final bool canPop;
  final Widget? topWidget;
  String topButtonText;
  dynamic topButtonLeftIcon;
  VoidCallback? topButtonOnPressed;
  String? bottomButtonText;
  VoidCallback? bottomButtonOnPressed;
  final Widget? child;
  final bool isTopButtonOutlined;
  final bool isBottomButtonOutlined;
  final Axis buttonLayoutDirection;
  String? extraButtonText;
  VoidCallback? extraButtonOnPressed;
  final bool isExtraButtonOutlined;

  bool? isInput = false;
  String? inputValue;
  final isTopButtonDisabled = false.obs;
  final isButtonDisabled = false.obs;
  int? inputMaxLength;
  TextInputType? inputKeyboardType;
  TextInputFormatter? inputFormatter;
  bool? closeOnConfirm;

  static final dialogDescriptionTextStyle = AppTheme.medium16Black.copyWith(color: const Color(0xFF87898E), height: 1.5);

  static final boldDialogDescriptionTextStyle = dialogDescriptionTextStyle.copyWith(fontWeight: AppFontWeight.bold);

  CMBottomSheet({
    this.canPop = true,
    required this.child,
  })  : title = null,
        itemsAlignment = CrossAxisAlignment.start,
        description = null,
        descriptionWidget = null,
        topWidget = null,
        topButtonText = '',
        topButtonOnPressed = null,
        isTopButtonOutlined = false,
        isBottomButtonOutlined = true,
        bottomButtonText = null,
        bottomButtonOnPressed = null,
        buttonLayoutDirection = Axis.vertical,
        extraButtonText = null,
        extraButtonOnPressed = null,
        isExtraButtonOutlined = true;

  CMBottomSheet.simple({
    this.title,
    this.description,
    this.descriptionWidget,
    this.itemsAlignment = CrossAxisAlignment.start,
    this.topWidget,
    this.topButtonLeftIcon,
    String buttonText = 'FECHAR',
    VoidCallback? buttonOnPressed,
    this.canPop = true,
    this.isTopButtonOutlined = false,
    this.isBottomButtonOutlined = true,
  })  : topButtonText = buttonText,
        topButtonOnPressed = buttonOnPressed ?? Get.back,
        bottomButtonOnPressed = Get.back,
        child = null,
        buttonLayoutDirection = Axis.vertical,
        extraButtonText = null,
        extraButtonOnPressed = null,
        isExtraButtonOutlined = true;

  CMBottomSheet.choice({
    this.title,
    this.description,
    this.descriptionWidget,
    this.itemsAlignment = CrossAxisAlignment.start,
    this.topWidget,
    this.topButtonLeftIcon,
    required this.topButtonText,
    required this.topButtonOnPressed,
    this.isTopButtonOutlined = false,
    required this.bottomButtonText,
    required this.bottomButtonOnPressed,
    this.isBottomButtonOutlined = true,
    this.extraButtonText,
    this.extraButtonOnPressed,
    this.isExtraButtonOutlined = true,
    this.canPop = true,
    this.closeOnConfirm = true,
    this.buttonLayoutDirection = Axis.vertical,
  }) : child = null;

  CMBottomSheet.input({
    this.title,
    this.description,
    this.descriptionWidget,
    this.itemsAlignment = CrossAxisAlignment.start,
    this.topWidget,
    this.topButtonLeftIcon,
    required this.topButtonText,
    required this.topButtonOnPressed,
    this.isTopButtonOutlined = false,
    required this.bottomButtonText,
    required this.bottomButtonOnPressed,
    this.isBottomButtonOutlined = true,
    this.extraButtonText,
    this.extraButtonOnPressed,
    this.isExtraButtonOutlined = true,
    this.canPop = true,
    this.inputFormatter,
    this.inputKeyboardType,
    this.inputMaxLength,
    this.isInput,
    this.inputValue,
    this.closeOnConfirm = true,
    this.buttonLayoutDirection = Axis.vertical,
  }) : child = null;

  bool get _isButtonLayoutVertical => buttonLayoutDirection == Axis.vertical;

  Future<T?> show<T>() {
    return Get.bottomSheet<T?>(
      PopScope(
        canPop: canPop,
        child: Container(
          margin: EdgeInsets.only(top: Get.mediaQuery.viewPadding.top),
          child: ClipRRect(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(10)),
            child: SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: Container(
                padding: const EdgeInsets.all(30),
                color: AppTheme.whiteColor,
                child: child ??
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: itemsAlignment,
                      children: [
                        if (topWidget != null) topWidget!,
                        if (title != null)
                          Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: Text(
                              title!,
                              style: AppTheme.bold18Black,
                              textAlign: TextAlign.start,
                            ),
                          ),
                        if (description != null)
                          Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: Text(
                              description!,
                              style: dialogDescriptionTextStyle,
                              textAlign: TextAlign.start,
                            ),
                          ),
                        if (descriptionWidget != null)
                          Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: descriptionWidget,
                          ),
                        if (isInput == true)
                          Padding(
                            padding: const EdgeInsets.only(right: 24.0),
                            child: Input(
                              onChanged: (value) => inputValue = value,
                              autofocus: true,
                              darkMode: false,
                              maxLength: inputMaxLength,
                              keyboardType: inputKeyboardType,
                              inputFormatters: inputFormatter != null ? [inputFormatter!] : null,
                            ),
                          ),
                        Obx(
                          () => Flex(
                            direction: buttonLayoutDirection,
                            children: [
                              Expanded(
                                flex: _isButtonLayoutVertical ? 0 : 1,
                                child: Button(
                                  buttonType: isTopButtonOutlined ? ButtonType.outlined : ButtonType.elevated,
                                  text: topButtonText,
                                  textStyle: isTopButtonOutlined ? AppTheme.medium14Orange : AppTheme.medium14White,
                                  leftIcon: topButtonLeftIcon,
                                  onPressed: () async {
                                    dynamic value = isInput == true ? inputValue : true;
                                    if (closeOnConfirm == true) Get.back(result: value);
                                    await _handleButtonClick(topButtonOnPressed);
                                  },
                                  disabled: isButtonDisabled.value || isTopButtonDisabled.value,
                                ),
                              ),
                              if (bottomButtonText != null)
                                Expanded(
                                  flex: _isButtonLayoutVertical ? 0 : 1,
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                      top: _isButtonLayoutVertical ? 10 : 0,
                                      left: _isButtonLayoutVertical ? 0 : 10,
                                    ),
                                    child: Button(
                                      buttonType: isBottomButtonOutlined ? ButtonType.outlined : ButtonType.elevated,
                                      text: bottomButtonText!,
                                      textStyle: isBottomButtonOutlined ? AppTheme.medium14Orange : AppTheme.medium14White,
                                      onPressed: () async => await _handleButtonClick(bottomButtonOnPressed),
                                      disabled: isButtonDisabled.value,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        if (extraButtonText != null)
                          Obx(
                            () => Button(
                              margin: const EdgeInsets.only(top: 10),
                              buttonType: isExtraButtonOutlined ? ButtonType.outlined : ButtonType.elevated,
                              text: extraButtonText!,
                              textStyle: isExtraButtonOutlined ? AppTheme.medium14Orange : AppTheme.medium14White,
                              onPressed: () async => await _handleButtonClick(extraButtonOnPressed),
                              disabled: isButtonDisabled.value,
                            ),
                          ),
                      ],
                    ),
              ),
            ),
          ),
        ),
      ),
      enableDrag: canPop,
      isDismissible: canPop,
      isScrollControlled: true,
    );
  }

  Future<void> _handleButtonClick(Function()? buttonAction) async {
    isButtonDisabled.value = true;
    await buttonAction?.call();
    isButtonDisabled.value = false;
  }
}
