import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../api/shared/shared_api.dart';
import '../../config/app_theme.dart';
import '../../errors/error_handlers.dart';
import '../../utils/integrations.dart';
import '../../utils/toast.dart';
import '../../utils/validators.dart';
import '../input/input.dart';
import '../buttons/button.dart';
import 'cm_bottom_sheet.dart';
import '../info/decorated_text.dart';

abstract class AppRatingDialogs {
  static final _sharedApi = Get.find<SharedApi>();

  static CMBottomSheet get ratingRequest => CMBottomSheet(
        child: Column(
          children: [
            SvgPicture.asset(
              'assets/icons/rating_app.svg',
            ),
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 25),
              child: Text(
                'O que você está achando do app CM Capital?',
                style: AppTheme.regular14Black,
              ),
            ),
            Button.elevated(
              text: 'AVALIAR AGORA',
              margin: const EdgeInsets.only(bottom: 16),
              onPressed: () {
                _respondRatingRequestDialog(AppRatingRequestResponse.rateNow);
                openAppStorePage();
              },
            ),
            Button.outlined(
              text: 'AVALIAR DEPOIS',
              textStyle: AppTheme.medium14Grey2,
              margin: const EdgeInsets.only(bottom: 16),
              onPressed: () => _respondRatingRequestDialog(AppRatingRequestResponse.rateLater),
            ),
            GestureDetector(
              onTap: () => _respondRatingRequestDialog(AppRatingRequestResponse.dontAskMeAgain),
              child: const Text(
                'Não me mostre essa mensagem novamente',
                style: AppTheme.regular12Grey2,
              ),
            ),
          ],
        ),
      );

  static void _respondRatingRequestDialog(AppRatingRequestResponse response) {
    Get.back();
    _sharedApi.respondAppRatingRequestDialog(response).ignore();
  }

  static CMBottomSheet get opinionOnApp => CMBottomSheet.choice(
        title: 'Queremos a sua opinião',
        descriptionWidget: DecoratedText(
          'Está gostando de usar o aplicativo da {CM Capital}?',
          textStyle: CMBottomSheet.dialogDescriptionTextStyle,
          decoratedTextStyle: CMBottomSheet.boldDialogDescriptionTextStyle,
        ),
        topButtonText: 'GOSTEI',
        topButtonOnPressed: () {
          Get.back();
          openAppStorePage();
        },
        bottomButtonText: 'NÃO GOSTEI',
        bottomButtonOnPressed: () {
          Get.back();
          complaint.show();
        },
      );

  static CMBottomSheet get complaint {
    String complaint = '';
    final formKey = GlobalKey<FormState>();

    return CMBottomSheet(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Queremos a sua opinião',
            style: AppTheme.bold18Black,
          ),
          const SizedBox(height: 16),
          Text(
            'Por favor, deixe o seu comentário. O nosso time de desenvolvimento irá trata-lo assim que recebido.',
            style: CMBottomSheet.dialogDescriptionTextStyle,
          ),
          const SizedBox(height: 8),
          Form(
            key: formKey,
            child: Input.filled(
              hintText: 'Digite aqui seus comentários',
              onChanged: (text) => complaint = text,
              maxLines: 5,
              validator: validateRequiredField,
              contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              fillColor: const Color(0xFFF5F5FA),
            ),
          ),
          Button.elevated(
            text: 'ENVIAR AVALIAÇÃO',
            onPressed: () async {
              if (formKey.currentState?.validate() != true) return;
              await _sendComplaintAboutTheApp(complaint);
            },
            margin: const EdgeInsets.only(bottom: 10),
          ),
          Button.outlined(
            text: 'AVALIAR APP',
            textStyle: AppTheme.medium14Orange,
            onPressed: () {
              Get.back();
              openAppStorePage();
            },
          ),
        ],
      ),
    );
  }

  static Future<void> _sendComplaintAboutTheApp(String complaint) async {
    try {
      await _sharedApi.sendComplaintAboutTheApp(complaint);
      Get.back();
      Toast.success(message: 'Sua avaliação foi enviada').show();
    } catch (error) {
      Get.back();
      onError(error);
    }
  }
}

enum AppRatingRequestResponse {
  rateNow(0),
  rateLater(1),
  dontAskMeAgain(2);

  final int id;

  const AppRatingRequestResponse(this.id);
}
