import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../config/constants.dart';
import '../../pages/tabs/transfers/deposit_method_page.dart';
import '../../utils/clipboard.dart';
import '../../utils/extensions.dart';
import '../card/pix_key_card.dart';
import '../info/decorated_text.dart';
import 'cm_bottom_sheet.dart';

abstract class PixModals {
  static CMBottomSheet get keyCopiedBottomSheet {
    return CMBottomSheet.simple(
      title: 'Chave Pix Copiada!',
      topWidget: Image.asset('assets/icons/dialog/redeem_requested.png'),
      descriptionWidget: DecoratedText(
        'Abra o app do seu banco, transfira o valor desejado e comece a investir!'
        '\n\n{Lembre-se: Só serão efetivados depósitos originados de uma conta com a mesma titularidade (CPF ou CNPJ) da sua conta CM Capital!}'
        '\n\n{Essa é a única chave Pix da CM Capital.}',
        textStyle: CMBottomSheet.dialogDescriptionTextStyle,
        decoratedTextStyle: CMBottomSheet.boldDialogDescriptionTextStyle,
      ),
      isTopButtonOutlined: true,
    );
  }

  static CMBottomSheet get newDepositMethodBottomSheet {
    return CMBottomSheet.choice(
      title: 'Novidade: Depósito com Pix!',
      topWidget: const _PixIcon(),
      descriptionWidget: DecoratedText(
        '{Você pediu e a CM Capital atendeu!}'
        '\nA funcionalidade de depósitos com Pix já está disponível para facilitar sua jornada de investidor! E esse é apenas o primeiro passo. Fique atento para mais funcionalidades relacionadas ao Pix em breve.',
        textStyle: CMBottomSheet.dialogDescriptionTextStyle,
        decoratedTextStyle: CMBottomSheet.boldDialogDescriptionTextStyle,
      ),
      topButtonText: 'Faça seu primeiro Pix',
      bottomButtonText: 'Fechar',
      topButtonOnPressed: () => Get.toNamedAndPopExistent(DepositMethodPage.routeName),
      bottomButtonOnPressed: Get.back,
    );
  }

  static CMBottomSheet get keyInformationBottomSheet {
    return CMBottomSheet.choice(
      title: 'Faça um Pix e comece a investir!',
      topWidget: const _PixIcon(),
      descriptionWidget: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DecoratedText(
            'Transfira o valor desejado para a chave abaixo e comece a investir agora mesmo!'
            '\n\nDepósitos com Pix serão efetivados em dias úteis entre {6:30h e 23:59h} e precisam vir de uma conta com a {mesma titularidade}.',
            textStyle: CMBottomSheet.dialogDescriptionTextStyle,
            decoratedTextStyle: CMBottomSheet.boldDialogDescriptionTextStyle,
          ),
          const SizedBox(height: 16),
          PixKeyCard(
            onTap: () {
              Get.back();
              copyPixKey();
            },
          ),
        ],
      ),
      topButtonText: 'Copiar chave pix',
      topButtonLeftIcon: Icons.copy_rounded,
      topButtonOnPressed: copyPixKey,
      bottomButtonText: 'Fechar',
      bottomButtonOnPressed: Get.back,
    );
  }

  static void copyPixKey() {
    copyToClipboard(Constants.pixKey, showToast: false);
    keyCopiedBottomSheet.show();
  }
}

class _PixIcon extends StatelessWidget {
  const _PixIcon();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 34),
      child: Image.asset('assets/icons/pix.png', height: 80),
    );
  }
}
