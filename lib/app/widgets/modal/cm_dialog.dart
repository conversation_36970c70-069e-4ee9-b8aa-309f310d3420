import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../input/input.dart';
import '../buttons/button.dart';

class CMDialog {
  String title;
  dynamic description;
  bool showCancelButton;
  String confirmButtonText;
  Function? confirmButtonOnPressed;
  VoidCallback? cancelButtonOnPressed;
  String? cancelButtonText;
  bool obscureInputText;
  Widget? topWidget;

  // Configurações para input
  int? inputMaxLength;
  TextInputType? inputKeyboardType;
  TextInputFormatter? inputFormatter;

  /// Define se o dialog pode ser dispensado ao clicar fora dele.
  /// Se false, obriga o usuário a clicar em algum botão no dialog para poder dispensar o dialog.
  /// Por padrão, é false.
  bool barrierDismissible;

  /// Define se o modal é fechado após a ação do botão de confirmar ser concluída. Por padrão, é [true].
  bool closeOnConfirm;

  // privates variables
  bool _isSuccess = false;
  bool _isWarning = false;
  bool _isInput = false;
  String? _inputValue;

  CMDialog({
    required this.title,
    this.description,
    this.topWidget,
    this.showCancelButton = false,
    this.cancelButtonOnPressed,
    this.confirmButtonOnPressed,
    this.confirmButtonText = 'Entendi',
    this.cancelButtonText = 'Não',
    this.barrierDismissible = false,
    this.closeOnConfirm = true,
  }) : obscureInputText = false;

  CMDialog.success({
    required this.description,
    this.topWidget,
    this.confirmButtonOnPressed,
    this.barrierDismissible = false,
    this.closeOnConfirm = true,
  })  : title = 'Sucesso!',
        showCancelButton = false,
        confirmButtonText = 'OK',
        cancelButtonText = 'Não',
        obscureInputText = false,
        _isSuccess = true;

  CMDialog.input({
    required this.title,
    required this.description,
    this.topWidget,
    this.confirmButtonOnPressed,
    this.obscureInputText = true,
    this.barrierDismissible = false,
    this.closeOnConfirm = true,
    this.inputMaxLength,
    this.inputKeyboardType,
    this.inputFormatter,
  })  : showCancelButton = true,
        confirmButtonText = 'Confirmar',
        cancelButtonText = 'Cancelar',
        _isInput = true;

  CMDialog.warning({
    required this.title,
    required this.description,
    this.topWidget,
    this.cancelButtonOnPressed,
    this.confirmButtonOnPressed,
    this.showCancelButton = true,
    this.confirmButtonText = 'Sim',
    this.cancelButtonText = 'Não',
    this.barrierDismissible = false,
    this.closeOnConfirm = true,
  })  : obscureInputText = false,
        _isWarning = true;

  Widget? _getDialogDescription() {
    const textStyle = TextStyle(
      color: AppTheme.blackColor,
      fontSize: 14,
      height: 1.5,
      fontWeight: FontWeight.normal,
    );

    if (description is String) {
      return Text(
        description,
        style: textStyle,
        textAlign: TextAlign.center,
      );
    }

    if (description is List<String>) {
      List<Container> strings = [];

      for (var string in description) {
        strings.add(Container(
          margin: const EdgeInsets.only(bottom: 20),
          child: Text(
            string,
            style: textStyle,
            textAlign: TextAlign.center,
          ),
        ));
      }

      return Column(
        children: [
          ...strings.toList(),
        ],
      );
    }

    return null;
  }

  Future show() {
    return Get.dialog(
      AlertDialog(
        title: Column(
          children: [
            if (topWidget != null)
              topWidget!
            else if (_isSuccess || _isWarning)
              Container(
                margin: const EdgeInsets.only(bottom: 10),
                child: SvgPicture.asset(
                  _isSuccess ? 'assets/icons/blue_circle_success.svg' : 'assets/icons/blue_circle_warning.svg',
                ),
              ),
            Text(
              title,
              textAlign: TextAlign.center,
              style: AppTheme.extraBold18White.copyWith(color: AppTheme.blueColor),
            )
          ],
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        contentPadding: const EdgeInsets.fromLTRB(24.0, 20.0, 0, 24.0),
        content: Container(
          constraints: BoxConstraints(maxHeight: Get.height * 0.80),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Flexible(
                child: Theme(
                  data: ThemeData(
                    scrollbarTheme: ScrollbarThemeData(
                      trackBorderColor: MaterialStateProperty.all(AppTheme.orangeColor),
                      thumbColor: MaterialStateProperty.all(AppTheme.orangeColor),
                      trackColor: MaterialStateProperty.all(AppTheme.orangeColor),
                    ),
                  ),
                  child: Scrollbar(
                    thumbVisibility: true,
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Container(
                        margin: const EdgeInsets.only(right: 24.0),
                        child: _getDialogDescription(),
                      ),
                    ),
                  ),
                ),
              ),
              if (_isInput)
                Padding(
                  padding: const EdgeInsets.only(right: 24.0),
                  child: Input(
                    autofocus: true,
                    onChanged: (value) => _inputValue = value,
                    darkMode: false,
                    obscureText: obscureInputText,
                    maxLength: inputMaxLength,
                    keyboardType: inputKeyboardType,
                    inputFormatters: inputFormatter != null ? [inputFormatter!] : null,
                  ),
                ),
              const SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.only(right: 24.0),
                child: Wrap(
                  alignment: showCancelButton ? WrapAlignment.spaceBetween : WrapAlignment.center,
                  spacing: 10,
                  runSpacing: 10,
                  children: <Widget>[
                    IntrinsicWidth(
                      child: Visibility(
                        visible: showCancelButton,
                        child: Button.outlined(
                          text: cancelButtonText ?? '',
                          textStyle: AppTheme.medium14Orange,
                          onPressed: () {
                            cancelButtonOnPressed?.call();
                            Get.back(result: false);
                          },
                        ),
                      ),
                    ),
                    IntrinsicWidth(
                      child: Button.elevated(
                        text: confirmButtonText,
                        onPressed: () async {
                          dynamic value = _isInput ? _inputValue : true;
                          if (confirmButtonOnPressed != null) {
                            value = await confirmButtonOnPressed!();
                          }
                          if (closeOnConfirm == true) Get.back(result: value);
                        },
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
      barrierDismissible: barrierDismissible,
    );
  }
}
