import 'package:flutter/material.dart';

import '../../config/app_theme.dart';

class TabButtonsCarousel extends StatefulWidget {
  final List<TabButton> buttons;
  final int? selected;

  const TabButtonsCarousel({super.key, required this.buttons, this.selected});

  @override
  State<TabButtonsCarousel> createState() => _TabButtonsCarouselState();
}

class _TabButtonsCarouselState extends State<TabButtonsCarousel> {
  int _selectedButtonIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      margin: const EdgeInsets.symmetric(vertical: 20),
      child: ListView.builder(
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 15),
          itemBuilder: (context, index) => Container(
                padding: const EdgeInsets.symmetric(horizontal: 5),
                child: TabButton(
                  text: widget.buttons[index].text,
                  onPressed: () {
                    widget.buttons[index].onPressed?.call();
                    setState(() {
                      _selectedButtonIndex = index;
                    });
                  },
                  isActived: widget.selected != null ? widget.selected == index : _selectedButtonIndex == index,
                ),
              ),
          itemCount: widget.buttons.length),
    );
  }
}

class TabButton extends StatelessWidget {
  final bool isActived;
  final int? tabKey;
  final String text;
  final void Function()? onPressed;

  const TabButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.tabKey,
    this.isActived = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Center(
        child: Container(
          height: 30,
          width: 90,
          decoration: BoxDecoration(
            color: isActived ? AppTheme.lightBlueColor : Colors.transparent,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: AppTheme.lightBlueColor),
          ),
          child: Align(
            alignment: Alignment.center,
            child: Text(
              text,
              style: AppTheme.smallText(
                color: AppTheme.whiteColor,
                fontWeight: FontWeight.w500,
                hasHeight: false,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}
