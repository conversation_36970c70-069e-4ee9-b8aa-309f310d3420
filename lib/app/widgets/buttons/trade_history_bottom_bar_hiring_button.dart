import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../controllers/trade/trade_hiring_controller.dart';
import '../../models/trade/trade.dart';
import 'button.dart';

class TradeHistoryBottomBarHiringButton extends StatelessWidget {
  final Trade trade;
  final String hiringRouteName;

  const TradeHistoryBottomBarHiringButton({
    super.key,
    required this.trade,
    required this.hiringRouteName,
  });

  @override
  Widget build(BuildContext context) {
    // Caso tenha sido contratao, mas desativado, não deve ser possível editar.
    if (trade.isHired && !trade.isActive) return Container();

    // Área com botão para tela de contratação.
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFF0E1E2B),
        border: Border(top: BorderSide(color: Color(0xFF00375F), width: 1)),
      ),
      child: But<PERSON>(
        buttonType: trade.isHired ? ButtonType.outlined : ButtonType.elevated,
        text: trade.isHired ? 'Editar' : 'Ativar',
        onPressed: () {
          TradeHiringController.tryNavigatingToHiringPage(trade: trade, routeName: hiringRouteName);
        },
        margin: const EdgeInsets.fromLTRB(16, 32, 16, 42).add(AppTheme.horizontalPadding) as EdgeInsets,
      ),
    );
  }
}
