import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../pages/tabs/transfers/deposit_method_page.dart';
import '../../utils/extensions.dart';
import 'button.dart';

class DepositButtons extends StatelessWidget {
  final String? text;
  const DepositButtons({super.key, this.text});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Texto
        if ((text ?? '').isNotEmpty)
          Flexible(
            child: Padding(
              padding: const EdgeInsets.only(bottom: 10),
              child: Text(
                text!,
                style: AppTheme.semi12White,
              ),
            ),
          ),

        // Botões Pix e TED
        Row(
          children: [
            Flexible(
              child: Button.elevated(
                height: 42,
                text: 'Pix',
                leftIcon: Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: SvgPicture.asset('assets/icons/pix.svg'),
                ),
                onPressed: () => Get.toNamedAndPopExistent(DepositMethodPage.routeName),
              ),
            ),
            const SizedBox(width: 15),
            Flexible(
              child: Button.outlined(
                height: 42,
                text: 'TED',
                leftIcon: Icons.currency_exchange,
                onPressed: () => Get.toNamedAndPopExistent(DepositMethodPage.routeName, arguments: 1),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
