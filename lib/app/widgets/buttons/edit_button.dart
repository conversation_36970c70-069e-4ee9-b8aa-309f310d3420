import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../config/app_theme.dart';

class EditButton extends StatelessWidget {
  final Color color;
  final VoidCallback onPressed;
  final EdgeInsets padding;
  const EditButton({
    super.key,
    this.color = AppTheme.greyColor,
    required this.onPressed,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: GestureDetector(
        onTap: onPressed,
        child: SvgPicture.asset(
          'assets/icons/field_edit.svg',
          colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
          height: 20,
        ),
      ),
    );
  }
}
