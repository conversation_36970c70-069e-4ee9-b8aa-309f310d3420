import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../modal/cm_bottom_sheet.dart';

class HelpIconButton extends StatelessWidget {
  final String titleText;
  final String bodyText;
  final String buttonText;

  /// Botão com ícone de ajuda que abre um `BottomSheet` com as informações passadas.
  const HelpIconButton({
    super.key,
    required this.titleText,
    required this.bodyText,
    this.buttonText = 'ENTENDI',
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      visualDensity: VisualDensity.compact,
      splashColor: Colors.transparent,
      splashRadius: 16,
      iconSize: 18,
      icon: const Icon(Icons.help_outline, color: AppTheme.greyColor2),
      onPressed: () => CMBottomSheet.simple(
        title: titleText,
        description: bodyText,
        buttonText: buttonText,
      ).show().ignore(),
    );
  }
}
