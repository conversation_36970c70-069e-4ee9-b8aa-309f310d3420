import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../config/constants.dart';
import '../../models/trade/trade_position.dart';
import '../../utils/extensions.dart';
import '../buttons/button.dart';
import '../info/amount_colored_chip.dart';
import 'card_with_border_header_and_body.dart';

class TradeClosedPositionStructuredOperationCard extends StatelessWidget {
  const TradeClosedPositionStructuredOperationCard({
    super.key,
    required this.position,
    required this.onDetailsPressed,
  });

  final TradePosition position;
  final Function() onDetailsPressed;

  @override
  Widget build(BuildContext context) {
    return CardWithBorderHeaderAndBody(
      // Cabeçalho
      headerPadding: const EdgeInsets.fromLTRB(16, 12, 4, 12),
      header: Row(
        children: [
          // Nome
          Expanded(child: Text(position.operationTitle ?? '', style: AppTheme.semi12White)),

          // Detalhes
          if (position.operationHadEnoughBalance)
            Button.text(
              height: 24,
              width: 110,
              text: 'Detalhes',
              isTextUpperCase: false,
              textStyle: AppTheme.bold12Orange,
              rightIcon: Icons.chevron_right,
              padding: EdgeInsets.zero,
              rightIconSize: 18,
              onPressed: onDetailsPressed,
            )

          // Data da operação
          else
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const Text('Data da operação', style: AppTheme.regular10White),
                Text(position.operationStartDate?.toddMMyyyy() ?? '', style: AppTheme.semi12White),
              ],
            ),
        ],
      ),

      // Informações
      body: position.operationHadEnoughBalance
          ? Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Data da operação e Aplicado
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Data da operação', style: AppTheme.regular10White),
                    Text(position.operationStartDate?.toddMMyyyy() ?? '', style: AppTheme.semi12White),
                    const SizedBox(height: 12),
                    const Text('Valor inicial', style: AppTheme.regular10White),
                    Text(position.initialAmount?.asCurrency() ?? 'R\$ 0,00', style: AppTheme.semi12White),
                  ],
                ),

                // Valor final
                const SizedBox(width: 10),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 44),
                      const Text('Valor final', style: AppTheme.regular10White),
                      Text(position.finalAmount?.asCurrency() ?? '', style: AppTheme.semi12White),
                    ],
                  ),
                ),

                // Data de encerramento e Resultado Líquido
                const SizedBox(width: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Data de encerramento', style: AppTheme.regular10White),
                    Text(position.endDate?.toddMMyyyy() ?? '', style: AppTheme.semi12White),
                    const SizedBox(height: 12),
                    const Text('Resultado líquido', style: AppTheme.regular10White),
                    AmountColoredChip(
                      position.operationNetResult ?? 0,
                      margin: const EdgeInsets.only(top: 2),
                      fontSize: 12,
                      showPlusSign: true,
                      showAsterisk: position.closedPositionWasClosedEarly,
                    ),
                  ],
                ),
              ],
            )
          : const Text(
              Constants.tradeInsuficientBalanceMessage,
              style: AppTheme.regular12White,
            ),
    );
  }
}
