import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../models/trade/trade_position.dart';
import '../../utils/extensions.dart';
import 'card_with_border_header_and_body.dart';

class TradeOpenPositionSpotFuturesCard extends StatelessWidget {
  const TradeOpenPositionSpotFuturesCard({super.key, required this.position, this.isBmf = false});

  final TradePosition position;
  final bool isBmf;

  @override
  Widget build(BuildContext context) {
    return CardWithBorderHeaderAndBody(
      // Cabeçalho
      header: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Ticker
          Text(position.ticker ?? '', style: AppTheme.semi12White),

          // Tipo de operação
          Text(position.positionType?.description ?? '', style: AppTheme.semi12White),

          // Data da Recomendação
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              const Text('Data da Recomendação', style: AppTheme.regular10White),
              Text(position.operationStartDate?.toddMMyyyy() ?? '', style: AppTheme.semi12White),
            ],
          ),
        ],
      ),

      // Informações
      body: Row(
        children: [
          // Aplicado e Gain
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Aplicado', style: AppTheme.regular10White),
                Text(
                  isBmf ? position.numberOfContracts.formattedContract() : position.operationInvestedAmount.asCurrency(),
                  style: AppTheme.semi12White,
                ),
                const SizedBox(height: 12),
                const Text('Gain', style: AppTheme.regular10White),
                Text(
                  position.operationGain.toMaskedAmount(mask: isBmf ? AmountMask.points : AmountMask.currency),
                  style: AppTheme.semi12White,
                ),
              ],
            ),
          ),

          // Entrada e Loss
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Entrada', style: AppTheme.regular10White),
                Text(
                  position.entry.toMaskedAmount(mask: isBmf ? AmountMask.points : AmountMask.currency),
                  style: AppTheme.semi12White,
                ),
                const SizedBox(height: 12),
                const Text('Loss', style: AppTheme.regular10White),
                Text(
                  position.operationLoss.toMaskedAmount(mask: isBmf ? AmountMask.points : AmountMask.currency),
                  style: AppTheme.semi12White,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
