import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../info/border_info.dart';

class CardWithBorderHeaderAndBody extends StatelessWidget {
  const CardWithBorderHeaderAndBody({
    this.backgroundColor = AppTheme.blueColor7,
    this.backgroundColorAlpha = 0.3,
    required this.header,
    this.headerPadding = const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    required this.body,
    this.bodyPadding = const EdgeInsets.fromLTRB(16, 12, 16, 20),
  });

  final Color backgroundColor;
  final double backgroundColorAlpha;
  final Widget header;
  final EdgeInsets headerPadding;
  final Widget body;
  final EdgeInsets bodyPadding;

  @override
  Widget build(BuildContext context) {
    return BorderInfo(
      backgroundColor: backgroundColor.withValues(alpha: backgroundColorAlpha),
      padding: EdgeInsets.zero,
      child: Column(
        children: [
          // Cabe<PERSON><PERSON><PERSON>
          Padding(padding: headerPadding, child: header),

          // Informações
          const Divider(height: 1, color: AppTheme.blueColor5),
          Padding(
            padding: bodyPadding,
            child: body,
          ),
        ],
      ),
    );
  }
}
