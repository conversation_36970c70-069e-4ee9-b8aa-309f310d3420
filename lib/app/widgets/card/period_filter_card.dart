import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../utils/ui_utils.dart';
import '../buttons/button.dart';
import '../input/dropdown_search.dart';

const _monthList = ['<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>ro'];

class PeriodFilterCard extends StatelessWidget {
  final String title;
  final String subTitle;
  final int selectedMonth;
  final int selectedYear;
  final String buttonText;
  final ValueChanged<Map<String, int>> onPeriodChanged;
  final VoidCallback onAction;

  const PeriodFilterCard({
    super.key,
    required this.title,
    required this.subTitle,
    required this.buttonText,
    required this.selectedMonth,
    required this.selectedYear,
    required this.onPeriodChanged,
    required this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: AppTheme.cardMargin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: AppTheme.bold16White),
          const SizedBox(height: 10),
          if (subTitle.isNotEmpty) Text(subTitle, style: AppTheme.regular12White),
          const SizedBox(height: 10),
          Container(
            padding: AppTheme.cardPadding,
            width: double.maxFinite,
            decoration: AppTheme.cardWithBorderBoxDecoration.copyWith(color: Colors.transparent),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Flexible(
                      child: DropdownSearch<int>(
                        label: 'Mês',
                        initialItem: selectedMonth,
                        listItemDisplay: (month) => _monthList[month - 1],
                        onItemSelected: (month) => onPeriodChanged({"month": month!, "year": selectedYear}),
                        nullable: false,
                        onSearch: (_) async => List.generate(12, (index) => index + 1),
                        isSearchable: false,
                        textFieldStyle: AppTheme.regular14White,
                        prefixIcon: iconify('assets/icons/calendar.svg', size: 18, color: AppTheme.orangeColor),
                      ),
                    ),
                    const SizedBox(width: 20),
                    Flexible(
                      child: DropdownSearch<int>(
                        label: 'Ano',
                        initialItem: selectedYear,
                        listItemDisplay: (year) => year.toString(),
                        onItemSelected: (year) => onPeriodChanged({"month": selectedMonth, "year": year!}),
                        nullable: false,
                        onSearch: (_) async => List.generate(15, (index) => DateTime.now().year - index),
                        isSearchable: false,
                        textFieldStyle: AppTheme.regular14White,
                        prefixIcon: iconify('assets/icons/calendar.svg', size: 18, color: AppTheme.orangeColor),
                      ),
                    ),
                  ],
                ),
                Button.elevated(
                  text: buttonText,
                  onPressed: onAction,
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
