import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../api/investments/investments_api.dart';
import '../../api/investments/oferta-publica/oferta_publica_documento.dart';
import '../../config/app_theme.dart';
import '../../utils/extensions.dart';
import '../../utils/get_file.dart';

class DocumentListCard extends StatefulWidget {
  final List<dynamic>? documents;

  const DocumentListCard({super.key, required this.documents})
      : assert(documents == null || documents is List<OfertaPublicaDocumento> || documents is List<FundosDocumento> || documents is List<CarteiraDocumento>);

  @override
  State<StatefulWidget> createState() => _CMDocumentListCardState();
}

class _CMDocumentListCardState extends State<DocumentListCard> {
  dynamic _documentLoading;

  Future<void> download(dynamic documento) async {
    // Não faz download se já houver um documento sendo carregado
    if (_documentLoading != null) return;

    // Mostra indicador de carregamento
    setState(() => _documentLoading = documento);

    // Abre documento
    await downloadAndOpenDocument(documento);

    // Aguarda animação de abertura e esconde indicador de carregamento
    Future.delayed(const Duration(seconds: 1), () => setState(() => _documentLoading = null));
  }

  String name(dynamic documento) {
    switch (documento.runtimeType) {
      case FundosDocumento:
        return documento.tipoDocumento.nome;
      case OfertaPublicaDocumento:
      case CarteiraDocumento:
        return documento.nome;
      default:
        return 'Documento';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(10),
      margin: const EdgeInsets.only(top: 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(
          color: AppTheme.whiteColor.withOpacity(0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 20.0),
            child: Row(
              children: [
                SvgPicture.asset('assets/icons/folder.svg'),
                const SizedBox(width: 10),
                const Text(
                  'Documentos',
                  style: AppTheme.semi12Orange,
                ),
              ],
            ),
          ),
          Wrap(
            alignment: WrapAlignment.start,
            crossAxisAlignment: WrapCrossAlignment.center,
            runSpacing: 10,
            children: (widget.documents ?? []).isEmpty
                ? [const SizedBox(height: 30)]
                : widget.documents!
                    .map(
                      (documento) => Stack(
                        alignment: Alignment.center,
                        children: [
                          Visibility(
                            visible: _documentLoading != documento,
                            maintainSize: true,
                            maintainAnimation: true,
                            maintainState: true,
                            child: GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () => download(documento),
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: Text(
                                  name(documento),
                                  style: AppTheme.semi12White.copyWith(height: 1),
                                ),
                              ),
                            ),
                          ),
                          if (_documentLoading == documento)
                            const SizedBox(
                              height: 14.0,
                              width: 14.0,
                              child: CircularProgressIndicator(
                                backgroundColor: AppTheme.blueColor,
                                valueColor: AlwaysStoppedAnimation(AppTheme.orangeColor),
                                strokeWidth: 3.0,
                              ),
                            ),
                        ],
                      ),
                    )
                    .cast<Widget>()
                    .placeBetween(
                      ConstrainedBox(
                        constraints: const BoxConstraints.tightFor(height: 30),
                        child: VerticalDivider(
                          color: AppTheme.whiteColor.withOpacity(0.1),
                          width: 25,
                          thickness: 0.5,
                        ),
                      ),
                    )
                    .toList(),
          ),
        ],
      ),
    );
  }
}
