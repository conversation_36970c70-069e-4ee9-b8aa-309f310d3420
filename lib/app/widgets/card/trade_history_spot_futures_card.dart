import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../config/constants.dart';
import '../../models/trade/trade_position.dart';
import '../../utils/extensions.dart';
import '../info/gain_loss_chip.dart';
import 'card_with_border_header_and_body.dart';

class TradeHistorySpotFuturesCard extends StatelessWidget {
  const TradeHistorySpotFuturesCard({
    super.key,
    this.ticker,
    this.positionType,
    this.isBmf = false,
    this.performance,
    this.startDate,
    this.endDate,
    this.isHired = false,
    this.entryAmount,
    this.exitAmount,
    this.numberOfContracts,
    this.hadEnoughBalance = true,
    this.wasClosedEarly = false,
  });

  final String? ticker;
  final TradePositionType? positionType;
  final bool isBmf;
  final double? performance;
  final DateTime? startDate;
  final DateTime? endDate;
  final bool isHired;
  final double? entryAmount;
  final double? exitAmount;
  final int? numberOfContracts;
  final bool hadEnoughBalance;
  final bool wasClosedEarly;

  @override
  Widget build(BuildContext context) {
    return CardWithBorderHeaderAndBody(
      // Cabeçalho
      header: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Ticker
          Text(ticker ?? '', style: AppTheme.semi12White),

          // Tipo de operação
          Text(positionType?.description ?? '', style: AppTheme.semi12White),

          // Gain/Loss
          if (hadEnoughBalance)
            GainLossChip(
              performance ?? 0,
              isPoints: isBmf,
              showAsterisk: wasClosedEarly,
            )

          // Data da Recomendação
          else
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const Text('Data da Recomendação', style: AppTheme.regular10White),
                Text(startDate?.toddMMyyyy() ?? '', style: AppTheme.semi12White),
              ],
            ),
        ],
      ),

      // Informações
      body: hadEnoughBalance
          ? Row(
              children: [
                // Data recomendação e Aplicado
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Data recomendação', style: AppTheme.regular10White),
                      Text(startDate?.toddMMyyyy() ?? '', style: AppTheme.semi12White),
                      const SizedBox(height: 12),
                      Text(isHired ? 'Aplicado' : 'Entrada', style: AppTheme.regular10White),
                      Text(
                        isHired
                            ? isBmf
                                ? numberOfContracts.formattedContract()
                                : entryAmount.asCurrency()
                            : entryAmount.toMaskedAmount(mask: isBmf ? AmountMask.points : AmountMask.currency),
                        style: AppTheme.semi12White,
                      ),
                    ],
                  ),
                ),

                // Data encerramento e Resultado Bruto
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Data encerramento', style: AppTheme.regular10White),
                      Text(endDate?.toddMMyyyy() ?? '', style: AppTheme.semi12White),
                      const SizedBox(height: 12),
                      Text(isHired ? 'Resultado Bruto' : 'Saída', style: AppTheme.regular10White),
                      Text(
                        isHired ? exitAmount.toMaskedAmount(showPlusSign: isBmf) : exitAmount.toMaskedAmount(mask: isBmf ? AmountMask.points : AmountMask.currency),
                        style: AppTheme.semi12White,
                      ),
                    ],
                  ),
                ),
              ],
            )
          : const Text(
              Constants.tradeInsuficientBalanceMessage,
              style: AppTheme.regular12White,
            ),
    );
  }
}
