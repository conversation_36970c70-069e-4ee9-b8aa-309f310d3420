import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../config/app_theme.dart';
import '../../utils/extensions.dart';

class BankCard extends StatelessWidget {
  final Widget? leading;
  final String title;
  final Widget? trailing;
  final List<List<CMBankCardContent>> content;
  final WrapAlignment contentAlignment;
  final void Function()? onDelete;

  const BankCard({
    super.key,
    required this.title,
    this.leading,
    this.trailing,
    this.contentAlignment = WrapAlignment.spaceBetween,
    this.content = const [],
    this.onDelete,
  });

  Widget _trailing() {
    return InkWell(
      customBorder: const CircleBorder(),
      onTap: onDelete,
      child: Container(
        height: 48,
        width: 64,
        alignment: Alignment.centerRight,
        child: SvgPicture.asset(
          'assets/icons/navigation_close.svg',
          colorFilter: const ColorFilter.mode(AppTheme.errorColor, BlendMode.srcIn),
          height: 12,
        ),
      ),
    );
  }

  Widget _content() {
    if (content.isNotEmpty) {
      return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        itemCount: content.length,
        shrinkWrap: true,
        itemBuilder: (outerContext, outerIndex) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Wrap(
                alignment: contentAlignment,
                children: <Widget>[
                  ...content[outerIndex].map(
                    (i) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text(
                            i.title,
                            style: AppTheme.regular11White,
                          ),
                          Text(
                            i.data,
                            style: AppTheme.bold12White,
                          ),
                        ],
                      );
                    },
                  ).toList(),
                ].placeBetween(const SizedBox(width: 16)).toList(),
              ),
              const SizedBox(height: 16)
            ],
          );
        },
      );
    } else {
      return const SizedBox();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: AppTheme.lightBlueColor.withOpacity(0.25),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 10),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: AppTheme.defaultText.copyWith(fontWeight: FontWeight.bold),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                _trailing(),
              ],
            ),
          ),
          _content()
        ],
      ),
    );
  }
}

class CMBankCardContent {
  final String title;
  final String data;

  CMBankCardContent(this.title, this.data);
}
