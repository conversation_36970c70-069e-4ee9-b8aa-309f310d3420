import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../controllers/user_controller.dart';
import '../buttons/button.dart';
import 'side_border_card.dart';

class NonQualifiedInvestorRestriction extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final userController = Get.find<UserController>();

    return SideBorderCard(
      borderColor: AppTheme.redColor,
      padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "Exclusivo para Investidor Qualificado",
            style: AppTheme.bold14White,
          ),
          const Text(
            "Confira se você se enquadra nos critérios e atualize seu perfil",
            style: AppTheme.regular12White,
          ),
          const SizedBox(height: 32),
          Obx(
            () => Button.elevated(
              text: "ADEQUAR MEU PERFIL",
              leftIcon: Icons.how_to_reg,
              isLoading: userController.isFetchingQualificationTerm.value,
              onPressed: () => userController.openQualificationConfirmBottomSheet(),
            ),
          )
        ],
      ),
    );
  }
}
