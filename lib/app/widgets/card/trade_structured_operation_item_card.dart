import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import 'card_with_border_header_and_body.dart';

class TradeStructuredOperationItemCard extends StatelessWidget {
  const TradeStructuredOperationItemCard({
    super.key,
    required this.ticker,
    required this.quantity,
    required this.averagePrice,
    required this.operationType,
    required this.result,
  });

  final String ticker;
  final String quantity;
  final String averagePrice;
  final String operationType;
  final String result;

  @override
  Widget build(BuildContext context) {
    return CardWithBorderHeaderAndBody(
      // Cabeçalho
      headerPadding: const EdgeInsets.fromLTRB(16, 12, 4, 12),
      header: Align(
        alignment: Alignment.centerLeft,
        child: Text(ticker, style: AppTheme.semi12White),
      ),

      // Informações
      body: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Quantidade e tipo
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Quantidade', style: AppTheme.regular10White),
              Text(quantity, style: AppTheme.semi12White),
              const SizedBox(height: 12),
              const Text('Compra ou Venda', style: AppTheme.regular10White),
              Text(operationType, style: AppTheme.semi12White),
            ],
          ),

          // Preço Médio e resultado
          const SizedBox(width: 10),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Preço Médio', style: AppTheme.regular10White),
              Text(averagePrice, style: AppTheme.semi12White),
              const SizedBox(height: 12),
              const Text('Crédito ou Débito', style: AppTheme.regular10White),
              Text(result, style: AppTheme.semi12White),
            ],
          ),
        ],
      ),
    );
  }
}
