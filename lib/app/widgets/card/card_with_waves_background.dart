import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../config/app_theme.dart';

class Card<PERSON>ithWavesBackground extends StatelessWidget {
  final EdgeInsets margin;
  final Color backgroundColor;
  final double radius;
  final List<BoxShadow>? boxShadow;
  final double? width;
  final double? height;
  final EdgeInsets padding;
  final Widget child;

  const CardWithWavesBackground({
    super.key,
    this.margin = EdgeInsets.zero,
    this.backgroundColor = AppTheme.blueColor6,
    this.radius = 10,
    this.boxShadow,
    this.width,
    this.height,
    this.padding = EdgeInsets.zero,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(radius),
        boxShadow: boxShadow,
      ),
      width: width,
      height: height,
      padding: padding,
      child: Stack(
        children: [
          // Imagem de fundo
          Positioned(
            top: 50,
            left: -1,
            child: Transform.scale(
              scale: 1.4,
              child: Opacity(
                opacity: 0.2,
                child: SvgPicture.asset('assets/images/header-background.svg'),
              ),
            ),
          ),

          // Conteú<PERSON>
          child,
        ],
      ),
    );
  }
}
