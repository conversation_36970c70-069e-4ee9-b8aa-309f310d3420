import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../models/trade/trade_position.dart';
import '../../utils/extensions.dart';
import '../buttons/button.dart';
import '../info/amount_colored_chip.dart';
import 'card_with_border_header_and_body.dart';

class TradeHistoryStructuredOperationCard extends StatelessWidget {
  const TradeHistoryStructuredOperationCard({
    super.key,
    required this.operation,
    this.isBmf = false,
    required this.onDetailsPressed,
  });

  final TradePosition operation;
  final bool isBmf;
  final Function() onDetailsPressed;

  @override
  Widget build(BuildContext context) {
    return CardWithBorderHeaderAndBody(
      // Cabeçalho
      headerPadding: const EdgeInsets.fromLTRB(16, 12, 4, 12),
      header: Row(
        children: [
          // Nome
          Expanded(child: Text(operation.recommendationTitle ?? '', style: AppTheme.semi12White)),

          // Detalhes
          Button.text(
            height: 24,
            width: 110,
            text: 'Detalhes',
            isTextUpperCase: false,
            textStyle: AppTheme.bold12Orange,
            rightIcon: Icons.chevron_right,
            padding: EdgeInsets.zero,
            rightIconSize: 18,
            onPressed: onDetailsPressed,
          ),
        ],
      ),

      // Informações
      body: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Data da operação
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Data da operação', style: AppTheme.regular10White),
              Text(operation.operationStartDate?.toddMMyyyy() ?? '', style: AppTheme.semi12White),
            ],
          ),

          // Data de encerramento
          const SizedBox(width: 10),
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Data de encerramento', style: AppTheme.regular10White),
                Text(operation.endDate?.toddMMyyyy() ?? '', style: AppTheme.semi12White),
              ],
            ),
          ),

          // Gain/Loss
          const SizedBox(width: 10),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Gain/Loss', style: AppTheme.regular10White),
              AmountColoredChip(
                operation.recommendationResultPercentage ?? 0,
                mask: AmountMask.percentage,
                showPlusSign: true,
                fontSize: 12,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
