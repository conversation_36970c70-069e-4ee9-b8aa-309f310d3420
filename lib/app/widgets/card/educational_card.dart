import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../config/app_theme.dart';
import '../info/badge.dart';
import '../loading/loading.dart';
import '../buttons/button.dart';

class EducationalCard extends StatelessWidget {
  final String actionText;

  /// Útil para filtrar por id.
  final int? id;
  final String imageUrl;

  /// Indica o preço do conteúdo, caso disponível.
  final String? cost;
  final VoidCallback? onAction;
  final String? description;
  final String? title;
  final BoxConstraints? constraints;
  final bool actionDisabled;
  final Color? buttonColor;
  final Widget? contentWidget;

  /// Card composto por imagem, título e descrição com um botão elevado.
  const EducationalCard({
    super.key,
    this.id,
    required this.imageUrl,
    this.title,
    this.description,
    required this.actionText,
    this.onAction,
    this.cost,
    this.constraints,
    this.buttonColor,
    this.actionDisabled = false,
    this.contentWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          constraints: const BoxConstraints(maxHeight: 320).copyWith(
            maxHeight: constraints?.maxHeight,
            maxWidth: constraints?.maxWidth,
            minHeight: constraints?.minHeight,
            minWidth: constraints?.minWidth,
          ),
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(5.0)),
          ),
          margin: const EdgeInsets.only(bottom: 20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              AspectRatio(
                aspectRatio: 16 / 9,
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(topLeft: Radius.circular(5.0), topRight: Radius.circular(5.0)),
                  child: Image.network(
                    imageUrl,
                    fit: BoxFit.cover,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return const Loading();
                    },
                    errorBuilder: (context, child, error) {
                      return Container(
                        color: AppTheme.blueColor,
                        child: SvgPicture.asset(
                          'assets/images/cm-capital-icon-transparent.svg',
                          colorFilter: const ColorFilter.mode(AppTheme.whiteColor, BlendMode.srcIn),
                          height: 32,
                        ),
                      );
                    },
                    frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
                      if (wasSynchronouslyLoaded) {
                        return child;
                      }
                      return AnimatedOpacity(
                        opacity: frame == null ? 0 : 1,
                        duration: const Duration(seconds: 1),
                        curve: Curves.easeOut,
                        child: child,
                      );
                    },
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Container(
                  padding: const EdgeInsets.fromLTRB(10.0, 10.0, 10.0, 0.0),
                  decoration: const BoxDecoration(
                    color: AppTheme.whiteColor,
                    borderRadius: BorderRadius.only(bottomLeft: Radius.circular(5.0), bottomRight: Radius.circular(5.0)),
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                title ?? '',
                                style: AppTheme.semi14Black.copyWith(color: AppTheme.blackColor),
                              ),
                            ),
                            if ((cost ?? '').isNotEmpty)
                              CMBadge(
                                text: cost!,
                                backgroundColor: AppTheme.greenColor,
                                borderRadius: 5.0,
                              )
                          ],
                        ),
                        if (description != null)
                          SizedBox(
                            height: 55,
                            width: double.infinity,
                            child: Text(
                              description ?? '',
                              style: AppTheme.regular12Black,
                              overflow: TextOverflow.fade,
                            ),
                          ),
                        if (contentWidget != null) contentWidget!,
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Button.elevated(
            text: actionText,
            backgroundColor: buttonColor ?? ((actionText == 'Contratado' || actionText == 'Matriculado') ? AppTheme.greenColor : AppTheme.orangeColor),
            height: 40,
            margin: const EdgeInsets.symmetric(horizontal: 10.0),
            disabled: actionDisabled,
            onPressed: onAction ?? () {},
          ),
        ),
      ],
    );
  }
}
