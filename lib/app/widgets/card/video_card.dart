import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../config/app_theme.dart';
import '../../models/youtube_playlist.dart';
import '../../models/youtube_video.dart';
import '../../pages/tabs/educational/educational_video_detail_page.dart';
import '../integration/app_webview.dart';
import '../loading/loading.dart';

class VideoCard extends StatefulWidget {
  final dynamic videoData;
  final BoxConstraints? constraints;

  const VideoCard(
    this.videoData, {
    super.key,
    this.constraints,
  }) : assert(videoData is YoutubeVideo || videoData is YoutubePlaylist);

  @override
  State<VideoCard> createState() => _VideoCardState();
}

class _VideoCardState extends State<VideoCard> {
  final _formatDate = DateFormat('dd/MM/yyyy');
  late BoxConstraints _constraints;

  @override
  void initState() {
    super.initState();

    _constraints = const BoxConstraints.tightFor(height: 256).copyWith(
      maxHeight: widget.constraints?.maxHeight,
      maxWidth: widget.constraints?.maxWidth,
      minHeight: widget.constraints?.minHeight,
      minWidth: widget.constraints?.minWidth,
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget thumbnail = VideoThumbnail(widget.videoData);

    Widget title = Container(
      margin: const EdgeInsets.only(bottom: 10),
      child: Text(
        widget.videoData.title ?? '',
        style: AppTheme.semi14Black,
        overflow: TextOverflow.ellipsis,
        maxLines: 2,
      ),
    );

    Widget subtitle = Text(
      widget.videoData.description ?? '',
      style: AppTheme.regular12Black,
      overflow: TextOverflow.fade,
    );

    Widget? date = (widget.videoData is YoutubeVideo)
        ? Text(
            widget.videoData.date != null ? _formatDate.format(widget.videoData.date) : '',
            style: AppTheme.semi12Blue,
          )
        : null;

    return ConstrainedBox(
      constraints: _constraints,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          AspectRatio(
            aspectRatio: 16 / 9,
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(5),
                topRight: Radius.circular(5),
              ),
              child: Container(color: AppTheme.blackColor, child: thumbnail),
            ),
          ),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(5),
                  bottomRight: Radius.circular(5),
                ),
                color: AppTheme.whiteColor,
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 0.8,
                ),
              ),
              padding: const EdgeInsets.all(10.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  title,
                  Expanded(child: subtitle),
                  if (date != null) ...[const SizedBox(height: 10), date],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class VideoThumbnail extends StatelessWidget {
  final dynamic videoData;
  final Color? overlay;
  final BorderRadius radius;

  const VideoThumbnail(
    this.videoData, {
    super.key,
    this.overlay,
    this.radius = BorderRadius.zero,
  }) : assert(videoData is YoutubeVideo || videoData is YoutubePlaylist);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (videoData is YoutubeVideo) {
          Navigator.of(context).pushNamed(EducationalVideoDetailPage.routeName, arguments: videoData);
        } else {
          Get.to(() => AppWebView(
                'https://www.youtube.com/playlist?list=${videoData.code}',
                title: videoData.title,
              ));
        }
      },
      child: ClipRRect(
        borderRadius: radius,
        child: Image.network(
          videoData.imageUrl ?? '',
          fit: BoxFit.cover,
          // Utilizado para que vídeos com barras pretas preencham todo o espaço
          width: double.infinity,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return const Loading();
          },
          errorBuilder: (context, error, stackTrace) {
            return Stack(
              alignment: Alignment.center,
              fit: StackFit.expand,
              children: [
                Container(
                  color: AppTheme.blueColor,
                  child: SvgPicture.asset(
                    'assets/images/cm-capital-icon-transparent.svg',
                    colorFilter: const ColorFilter.mode(AppTheme.whiteColor, BlendMode.srcIn),
                    height: 32,
                  ),
                ),
                const Icon(
                  Icons.play_arrow_rounded,
                  color: AppTheme.whiteColor,
                  size: 60,
                ),
              ],
            );
          },
          frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
            Widget outerChild = Stack(
              alignment: Alignment.center,
              children: [
                child,
                Container(color: overlay),
                const Icon(
                  Icons.play_arrow_rounded,
                  color: AppTheme.whiteColor,
                  size: 60,
                ),
              ],
            );
            if (wasSynchronouslyLoaded) {
              return outerChild;
            }
            return AnimatedOpacity(
              opacity: frame == null ? 0 : 1,
              duration: const Duration(seconds: 1),
              curve: Curves.easeOut,
              child: outerChild,
            );
          },
        ),
      ),
    );
  }
}
