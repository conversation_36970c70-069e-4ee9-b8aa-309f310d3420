import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../models/trade/trade_position.dart';
import '../../utils/extensions.dart';
import '../buttons/button.dart';
import 'card_with_border_header_and_body.dart';

class TradeOpenPositionStructuredOperationCard extends StatelessWidget {
  const TradeOpenPositionStructuredOperationCard({
    super.key,
    required this.position,
    required this.onDetailsPressed,
  });

  final TradePosition position;
  final Function() onDetailsPressed;

  @override
  Widget build(BuildContext context) {
    return CardWithBorderHeaderAndBody(
      // Cabeçalho
      headerPadding: const EdgeInsets.fromLTRB(16, 12, 4, 12),
      header: Row(
        children: [
          // Nome
          Expanded(child: Text(position.operationTitle ?? '', style: AppTheme.semi12White)),

          // Detalhes
          Button.text(
            height: 24,
            width: 110,
            text: 'Detalhes',
            isTextUpperCase: false,
            textStyle: AppTheme.bold12Orange,
            rightIcon: Icons.chevron_right,
            padding: EdgeInsets.zero,
            rightIconSize: 18,
            onPressed: onDetailsPressed,
          ),
        ],
      ),

      // Informações
      body: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Aplicado e Gain
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Valor inicial', style: AppTheme.regular10White),
              Text(position.initialAmount?.asCurrency() ?? '', style: AppTheme.semi12White),
              const SizedBox(height: 12),
              const Text('Gain', style: AppTheme.regular10White),
              Text(position.operationGain?.asPtBrDecimal ?? '', style: AppTheme.semi12White),
            ],
          ),

          // Entrada e Loss
          const SizedBox(width: 10),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Ratio de Entrada', style: AppTheme.regular10White),
              Text(position.entry?.asPtBrDecimal ?? '', style: AppTheme.semi12White),
              const SizedBox(height: 12),
              const Text('Loss', style: AppTheme.regular10White),
              Text(position.operationLoss?.asPtBrDecimal ?? '', style: AppTheme.semi12White),
            ],
          ),

          // Data de início
          const SizedBox(width: 10),
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Data de início', style: AppTheme.regular10White),
                Text(position.operationStartDate?.toddMMyyyy() ?? '', style: AppTheme.semi12White),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
