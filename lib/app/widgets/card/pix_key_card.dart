import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../config/app_theme.dart';
import 'card_with_waves_background.dart';

class PixKeyCard extends StatelessWidget {
  final Color backgroundColor;
  final VoidCallback? onTap;

  const PixKeyCard({
    super.key,
    this.backgroundColor = AppTheme.blueColor6,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: CardWithWavesBackground(
        backgroundColor: backgroundColor,
        radius: 10,
        child: Row(
          children: [
            // Texto
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 34, right: 34, top: 17, bottom: 17),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Chave Pix:', style: AppTheme.medium12White),
                            Text('02.685.483/0001-30', style: AppTheme.bold18White),
                          ],
                        ),
                        SvgPicture.asset(
                          'assets/images/cm-capital-icon-transparent.svg',
                          colorFilter: const ColorFilter.mode(AppTheme.whiteColor, BlendMode.srcIn),
                          height: 32,
                        ),
                      ],
                    ),
                    const Text('Para:', style: AppTheme.medium12White),
                    const Text('CM CAPITAL MARKETS CCTVM LTDA', style: AppTheme.bold16White),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
