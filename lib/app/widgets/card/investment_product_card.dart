import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../api/investments/credito-privado/debenture_disponivel.dart';
import '../../api/investments/investments_api.dart';
import '../../api/investments/tesouro-direto/tesouro_direto_api.dart';
import '../../api/investments/tesouro-direto/tesouro_direto_titulo_disponivel.dart';
import '../../api/rentability/rentability_api.dart';
import '../../config/app_theme.dart';
import '../../pages/tabs/investments/investment_closed_public_offer_detail_page.dart';
import '../../pages/tabs/investments/investment_product_detail_page.dart';
import '../../pages/tabs/investments/purchase/fixed_income_purchase_detail_page.dart';
import '../../pages/tabs/investments/purchase/investment_funds_purchase_detail_page.dart';
import '../../pages/tabs/investments/purchase/private_credit_purchase_detail_page.dart';
import '../../pages/tabs/investments/purchase/treasury_purchase_detail_page.dart';
import '../../pages/tabs/rentability/rentability_detailed_product_page.dart';
import '../../utils/extensions.dart';
import '../../utils/ui_utils.dart';
import '../info/badge.dart';
import '../info/vertical_field.dart';
import '../percentage/percentage_chip.dart';

class InvestmentProductCard extends StatefulWidget {
  final AvailableProduct product;

  final BoxConstraints? constraints;

  const InvestmentProductCard({
    super.key,
    required this.product,
    this.constraints,
  });

  @override
  State<InvestmentProductCard> createState() => _InvestmentProductCardState();
}

class _InvestmentProductCardState extends State<InvestmentProductCard> {
  List<VerticalField>? fields;
  List<Widget>? leftBadges;
  List<Widget>? rightBadges;

  @override
  void initState() {
    super.initState();
    getBadges();
    getFields();
  }

  /// Obtém os campos e as badges a serem incluídas nos cards.
  void getBadges() {
    leftBadges = [
      if (widget.product.perfilInvestidor != null)
        CMBadge(
          text: widget.product.perfilInvestidor?.text ?? '',
          backgroundColor: widget.product.perfilInvestidor?.color ?? AppTheme.orangeColor,
          margin: EdgeInsets.zero,
        )
      else
        const SizedBox(),
      if (widget.product.productItself is OfertaPublica && (widget.product.productItself as OfertaPublica).private)
        const CMBadge(
          text: '★ Private',
          backgroundColor: AppTheme.whiteColor,
          margin: EdgeInsets.zero,
        )
    ];
    rightBadges = [
      if (widget.product.investidorQualificado)
        HighlightedIconBadge(iconify(Icons.how_to_reg, color: Colors.white, size: 20)) //
      else
        const SizedBox.shrink(),
      if ((widget.product.valor ?? '').isNotEmpty && widget.product.productItself is! AnaliseCarteiraPosicao)
        HighlightedValueBadge('${widget.product.productItself is OfertaPublica ? 'Preço' : 'Mínimo'}: ', widget.product.valor ?? '')
      else
        const SizedBox.shrink(),
    ];
  }

  /// Mapeia o nome e o valor dos campos para um conteúdo mais amigável ao usuário.
  void getFields() {
    List<VerticalField> mappedFields = [];
    if ((widget.product.productItself is AnaliseCarteiraPosicao)) {
      var produto = widget.product.productItself as AnaliseCarteiraPosicao;
      mappedFields.add(
        VerticalField(
          'L/P',
          Padding(
            padding: const EdgeInsets.only(top: 5),
            child: PercentageChip(produto.valorPorcentagemLP ?? 0, fontSize: 12),
          ),
        ),
      );
      mappedFields.add(VerticalField('Resultado', produto.valorResultado.asCurrency(), isValueSensitive: true));
      mappedFields.add(VerticalField('Proventos', produto.valorProventos.asCurrency(), isValueSensitive: true));
      mappedFields.add(VerticalField('Cotação Atual', produto.valorAtivo.asCurrency(), isValueSensitive: true));
      mappedFields.add(VerticalField('Preço Médio', produto.valorMedio.asCurrency(), isValueSensitive: true));
      if (produto.myAveragePrice != null) mappedFields.add(VerticalField('Meu Preço Médio', produto.myAveragePrice.asCurrency(), isValueSensitive: true));
      mappedFields.add(VerticalField('Quantidade', (produto.quantidadeTotal ?? 0).toInt().toString(), isValueSensitive: true));
      mappedFields.add(VerticalField('Ponto de Equilibrio', produto.valorPrecoAlvo.asCurrency(), isValueSensitive: true));
      fields = mappedFields;
      return;
    }

    if (widget.product.productItself is TesouroDiretoTituloDisponivel) {
      final titulo = widget.product.productItself as TesouroDiretoTituloDisponivel;
      mappedFields.add(VerticalField('Título', titulo.tipo ?? "-"));
      mappedFields.add(VerticalField('Rentabilidade', titulo.descricaoRentabilidade));
      mappedFields.add(VerticalField('Vencimento', titulo.vencimento?.toddMMyyyy() ?? "-"));
      fields = mappedFields;
      return;
    }

    for (var field in widget.product.toJson().entries.where((field) => field.value != null)) {
      String key = field.key.toLowerCase();
      switch (key) {
        case 'ativo':
          // O 'ativo' da oferta pública é exibido apenas como texto, sem necessidade de label.
          if (widget.product.productItself is OfertaPublica) {
            mappedFields.add(
              VerticalField(
                Text(field.value, style: AppTheme.semi11White),
                null,
                widthFactor: 1,
              ),
            );
          } else {
            mappedFields.add(
              VerticalField(
                'Ativo',
                field.value,
              ),
            );
          }
          break;
        // Campos de renda fixa
        case 'rentabilidade':
          // Fundos também tem rentabilidade, mas só deve ser exibido na página de detalhamento
          if (widget.product.productItself is RendaFixaTitulo || widget.product.productItself is DebentureDisponivel) {
            mappedFields.add(
              VerticalField('Rentabilidade', field.value),
            );
          }
          break;
        case 'vencimento':
          mappedFields.add(
            VerticalField('Vencimento', field.value),
          );
          break;
        case 'liquidez':
          mappedFields.add(
            VerticalField('Liquidez', field.value),
          );
          break;
        // Campos de oferta pública
        case 'fimparavinculados':
          if (field.value != null && field.value is String && field.value.contains(' às ')) {
            var value = (field.value as String).split(' às ');
            mappedFields.add(
              VerticalField(
                dateField(
                  'Fim para vinculados',
                  value[0],
                  value[1],
                ),
                null,
              ),
            );
          }
          break;
        case 'fimparanaovinculados':
          if (field.value != null && field.value is String && field.value.contains(' às ')) {
            var value = (field.value as String).split(' às ');
            mappedFields.add(
              VerticalField(
                dateField(
                  'Fim para não vinculados',
                  value[0],
                  value[1],
                ),
                null,
              ),
            );
          }
          break;
        case 'inicio':
          mappedFields.add(
            VerticalField('Início', field.value),
          );
          break;
        case 'faixaindicativa':
          mappedFields.add(
            VerticalField('Faixa Indicativa de Preço', field.value),
          );
          break;
        case 'minimo':
          mappedFields.add(
            VerticalField('Investimento Mínimo', field.value),
          );
          break;
        case 'maximo':
          mappedFields.add(
            VerticalField('Investimento Máximo', field.value),
          );
          break;

        // Campos de fundos
        case 'tipo':
          mappedFields.add(
            VerticalField('Tipo', field.value),
          );
          break;
        case 'cotizacaoderesgate':
          mappedFields.add(
            VerticalField('Cot. de Resgate', field.value),
          );
          break;
        case 'rentabilidadeano':
          mappedFields.add(
            VerticalField('Rent. Ano', field.value),
          );
          break;
        case 'rentabilidademes':
          mappedFields.add(
            VerticalField('Rent. Mês', field.value),
          );
          break;
        case 'rentabilidade12meses':
          mappedFields.add(
            VerticalField('Rent. 12 meses', field.value),
          );
          break;

        // Campos de crédito privado
        case 'taxa':
          mappedFields.add(
            VerticalField('Taxa de compra', field.value),
          );
          break;
        case 'rating':
          mappedFields.add(
            VerticalField('Rating', field.value),
          );
          break;
        case 'qualificacao':
          mappedFields.add(
            VerticalField('Público alvo', field.value),
          );
          break;
        default:
          break;
      }
    }
    fields = mappedFields;
  }

  /// Define a ação ao clicar no card. Por padrão, vai para a página de detalhamento do produto.
  Future<void> onTap() async {
    if (widget.product.productItself is OfertaPublica && (widget.product.productItself as OfertaPublica).ipoOferPubIsEncerrado == true) {
      return await Get.toNamed(ClosedPublicOfferDetailPage.routeName, arguments: widget.product.productItself);
    }
    if (widget.product.productItself is AnaliseCarteiraPosicao) {
      return await Get.toNamed(RentabilityDetailedProductPage.routeName, arguments: widget.product.productItself);
    }
    if (widget.product.productItself is RendaFixaTitulo) {
      return await Get.toNamed(FixedIncomePurchaseDetailPage.routeName, arguments: widget.product);
    }
    if (widget.product.productItself is TesouroDiretoTituloDisponivel) {
      return await Get.toNamed(TreasuryPurchaseDetailPage.routeName, arguments: widget.product);
    }
    if (widget.product.productItself is FundoDisponivel) {
      return await Get.toNamed(InvestmentFundsPurchaseDetailPage.routeName, arguments: widget.product);
    }
    if (widget.product.productItself is DebentureDisponivel) {
      return await Get.toNamed(PrivateCreditPurchaseDetailPage.routeName, arguments: widget.product);
    }
    return await Get.toNamed(InvestmentProductDetailPage.routeName, arguments: widget.product);
  }

  Widget dateField(String label, String dateValue, String hourValue) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: AppTheme.regular10White),
        Text(dateValue, style: AppTheme.semi12White),
        Text('às $hourValue', style: AppTheme.regular10White),
      ],
    );
  }

  String get _headerTitle {
    return widget.product.emissor ?? '';
  }

  dynamic get _headerIcon {
    switch (widget.product.productItself.runtimeType) {
      case AnaliseCarteiraPosicao:
        return 'assets/icons/circle_arrow_right.svg';
      case OfertaPublica:
        var oferta = widget.product.productItself as OfertaPublica;
        if (oferta.ipoOferPubIsEncerrado == true) {
          return Icons.search_rounded;
        }
        return Icons.shopping_cart;
      case RendaFixaTitulo:
      case FundoDisponivel:
      case DebentureDisponivel:
      case TesouroDiretoTituloDisponivel:
        return Icons.shopping_cart;
      default:
        return 'assets/icons/action_external.svg';
    }
  }

  Color get _backgroundColor {
    if (widget.product.destacado == true) return AppTheme.orangeColor;
    return AppTheme.blueColor;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(5),
      child: InkWell(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            color: _backgroundColor,
          ),
          constraints: widget.constraints,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CMProductCardHeader(
                title: _headerTitle,
                icon: _headerIcon,
                iconSize: widget.product.productItself is AnaliseCarteiraPosicao ? 30 : 24,
              ),
              CMProductCardBody(
                fields: fields ?? [],
                leftBadges: leftBadges ?? [],
                rightBadges: rightBadges ?? [],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class HighlightedValueBadge extends StatelessWidget {
  final String label;
  final String value;

  const HighlightedValueBadge(this.label, this.value, {super.key});

  @override
  Widget build(BuildContext context) {
    return IntrinsicWidth(
      child: Container(
        height: 32,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(40),
          border: Border.all(color: Colors.black38),
        ),
        child: RichText(
          text: TextSpan(
            text: label,
            style: AppTheme.regular10White.copyWith(height: 1),
            children: [
              TextSpan(
                text: value,
                style: AppTheme.semi16White.copyWith(height: 1),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class HighlightedIconBadge extends StatelessWidget {
  final dynamic icon;

  const HighlightedIconBadge(this.icon, {super.key});

  @override
  Widget build(BuildContext context) {
    return IntrinsicWidth(
      child: Container(
        height: 32,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.black38),
        ),
        child: icon,
      ),
    );
  }
}

class CMProductCardHeader extends StatelessWidget {
  final String title;
  final dynamic icon;
  final double iconSize;

  const CMProductCardHeader({
    super.key,
    required this.title,
    this.icon,
    this.iconSize = 24,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.black38,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          /// Título
          Expanded(
            child: Container(
              padding: const EdgeInsets.only(left: 15.0),
              child: Text(
                title,
                style: AppTheme.semi14White,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
          ),

          /// Ícone
          Container(
            alignment: Alignment.center,
            decoration: const BoxDecoration(
              border: Border(
                left: BorderSide(color: Colors.black38),
              ),
            ),
            width: 64,
            child: iconify(
              icon,
              color: AppTheme.whiteColor,
              size: iconSize,
            ),
          ),
        ],
      ),
    );
  }
}

class CMProductCardBody extends StatelessWidget {
  final List<VerticalField> fields;
  final List<Widget> leftBadges;
  final List<Widget> rightBadges;

  const CMProductCardBody({super.key, this.fields = const [], this.leftBadges = const [], this.rightBadges = const []});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(15),
      alignment: Alignment.bottomCenter,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        mainAxisSize: MainAxisSize.min,
        children: [
          Theme(
            data: ThemeData(
              textTheme: TextTheme(
                labelSmall: AppTheme.regular10White.copyWith(letterSpacing: 0.5),
                bodyLarge: AppTheme.semi12White,
              ),
            ),
            child: Wrap(crossAxisAlignment: WrapCrossAlignment.start, children: fields),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 15.0),
            child: Wrap(
              runSpacing: 6,
              alignment: WrapAlignment.spaceBetween,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                IntrinsicWidth(
                  child: Row(
                    children: leftBadges
                        .placeBetween(
                          const SizedBox(width: 6),
                        )
                        .toList(),
                  ),
                ),
                IntrinsicWidth(
                  child: Row(
                    children: rightBadges
                        .placeBetween(
                          const SizedBox(width: 6),
                        )
                        .toList(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
