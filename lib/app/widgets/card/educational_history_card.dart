import 'package:cm_capital_app/app/widgets/card/side_border_card.dart';
import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../info/vertical_field.dart';

class EducationalHistoryCard extends StatelessWidget {
  final int id;
  final String name;
  final String dateStart;
  final String dateEnd;
  final String amount;

  const EducationalHistoryCard({
    super.key,
    required this.id,
    required this.name,
    required this.dateStart,
    required this.dateEnd,
    required this.amount,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: SideBorderCard(
        borderWidth: 5,
        borderColor: AppTheme.blueColor4,
        child: Container(
          decoration: const BoxDecoration(
            color: AppTheme.blueColor4,
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(5),
              bottomRight: Radius.circular(5),
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(5),
            child: Container(
              padding: const EdgeInsets.all(10),
              color: AppTheme.blueColor,
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 3),
                    child: Text(
                      name,
                      style: AppTheme.semi14White,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Theme(
                    data: ThemeData(
                      textTheme: TextTheme(
                        labelSmall: AppTheme.regular11White.copyWith(letterSpacing: 0.5),
                        bodyLarge: AppTheme.semi12White,
                      ),
                    ),
                    child: Wrap(
                      runSpacing: 10,
                      children: [
                        VerticalField("Início", dateStart),
                        VerticalField("Conclusão", dateEnd),
                        VerticalField("Valor", amount),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
