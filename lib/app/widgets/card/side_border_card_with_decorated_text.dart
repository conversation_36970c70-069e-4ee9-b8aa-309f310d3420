import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../info/decorated_text.dart';
import 'side_border_card.dart';

class SideBorderCardWithDecoratedText extends StatelessWidget {
  final EdgeInsets margin;
  final Color borderColor;
  final String decoratedText;
  final TextStyle decoratedTextStyle;
  final List<BoxShadow>? boxShadow;

  const SideBorderCardWithDecoratedText({
    this.margin = const EdgeInsets.only(top: 24),
    required this.decoratedText,
    this.borderColor = AppTheme.greenColor3,
    this.decoratedTextStyle = AppTheme.semi14White,
    this.boxShadow = AppTheme.boxShadowBlack,
  });

  @override
  Widget build(BuildContext context) {
    return SideBorderCard(
      margin: margin,
      borderColor: borderColor,
      boxShadow: boxShadow,
      child: DecoratedText(
        decoratedText,
        textStyle: AppTheme.regular14White,
        decoratedTextStyle: decoratedTextStyle,
      ),
    );
  }
}

/// Aviso de mesma titularidade
const depositSameHolderWarning = SideBorderCardWithDecoratedText(
  borderColor: AppTheme.yellowColor2,
  decoratedTextStyle: AppTheme.bold14White,
  decoratedText: '{Importante:} só serão aceitos depósitos originados de contas bancárias com a mesma titularidade (CPF ou CNPJ) da sua conta CM Capital!',
);

/// Aviso de favorecido no pix
const depositPixNotificationWarning = SideBorderCardWithDecoratedText(
  borderColor: AppTheme.yellowColor2,
  decoratedTextStyle: AppTheme.bold14White,
  decoratedText: '{Atenção:} Se o método de depósito escolhido for o Pix, o nome do favorecido aparecerá como {CM Capital Markets CCTVM LTDA}. Esse é o comportamento esperado e o valor será creditado em sua conta normalmente.',
);

/// Aviso de notificação
const depositNotificationWarning = SideBorderCardWithDecoratedText(
  decoratedText: 'Você receberá uma notificação e um e-mail assim que o depósito for recebido na sua conta CM Capital.',
);
