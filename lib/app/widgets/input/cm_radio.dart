import 'package:flutter/material.dart';

import '../../config/app_theme.dart';

class CMRadio<T> extends StatelessWidget {
  final T value;
  final dynamic label;
  final TextStyle? labelStyle;
  final T groupValue;
  final ValueChanged<T?>? onChanged;
  final bool hasError;
  final bool disabled;

  const CMRadio({
    super.key,
    required this.value,
    required this.label,
    required this.groupValue,
    this.onChanged,
    this.hasError = false,
    this.disabled = false,
    this.labelStyle,
  });

  Widget get labelWidget {
    if (label is Widget) return label;
    if (label is String) return Text(label, style: labelStyle ?? AppTheme.regular12White);
    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    final correctOnChanged = disabled ? null : onChanged;

    return Theme(
      data: Theme.of(context).copyWith(
        radioTheme: RadioThemeData(
          fillColor: MaterialStateProperty.resolveWith((states) {
            if (hasError) return AppTheme.errorColor;
            if (states.contains(MaterialState.disabled)) return AppTheme.greyColor;
            return AppTheme.orangeColor;
          }),
        ),
      ),
      child: Opacity(
        opacity: correctOnChanged != null ? 1 : 0.5,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Radio(
              visualDensity: VisualDensity.compact,
              activeColor: AppTheme.orangeColor,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              value: value,
              groupValue: groupValue,
              onChanged: correctOnChanged,
            ),
            Flexible(
              child: GestureDetector(
                onTap: correctOnChanged != null ? () => onChanged!(value) : null,
                child: labelWidget,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
