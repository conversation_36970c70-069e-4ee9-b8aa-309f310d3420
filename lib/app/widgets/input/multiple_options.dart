import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

import '../../config/app_theme.dart';

class MultipleOptions extends StatefulWidget {
  final List<CMOption> options;
  final int itemsPerLine;
  final Function(int, CMOption) onChange;
  final bool isSingle;

  const MultipleOptions({
    super.key,
    required this.options,
    required this.itemsPerLine,
    required this.onChange,
    this.isSingle = false,
  });

  @override
  State<MultipleOptions> createState() => _CMMultipleOptionsState();
}

class _CMMultipleOptionsState extends State<MultipleOptions> {
  @override
  Widget build(BuildContext context) {
    return Wrap(
      runSpacing: 10,
      spacing: 10,
      alignment: WrapAlignment.start,
      children: widget.options
          .mapIndexed(
            (index, option) => InkWell(
              borderRadius: const BorderRadius.all(Radius.circular(5.0)),
              onTap: () {
                if (widget.isSingle) {
                  for (final option in widget.options) {
                    option.selected = false;
                  }
                  option.selected = true;
                } else {
                  option.selected = !option.selected;
                }
                widget.onChange(index, option);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12.5, horizontal: 24),
                decoration: BoxDecoration(
                  color: option.selected ? option.color : Colors.transparent,
                  borderRadius: const BorderRadius.all(Radius.circular(5.0)),
                  border: Border.all(color: option.selected ? option.color : AppTheme.whiteColor),
                ),
                child: Text(
                  option.label,
                  style: AppTheme.semi14White,
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          )
          .toList(),
    );
  }
}

class CMOption {
  final String label;
  final dynamic value;
  final Color color;
  bool selected;

  CMOption({
    required this.label,
    required this.value,
    this.color = AppTheme.orangeColor,
    this.selected = false,
  });
}
