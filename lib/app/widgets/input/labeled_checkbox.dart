import 'package:flutter/material.dart';

import '../../config/app_theme.dart';

/// Checkbox com possibilidade de alterar posição do ícone e usar um Widget como texto.
class LabeledCheckbox extends StatelessWidget {
  final Widget? customLabel;
  final String textLabel;
  final EdgeInsetsGeometry margin;
  final bool? value;
  final bool tristate;
  final bool disabled;
  final Function(bool)? onChanged;
  final Color color;
  final FormFieldValidator<bool>? validator;
  final AutovalidateMode autovalidateMode;
  final bool isSmallCheckbox;
  final Color? fillColor;
  final EdgeInsets errorPadding;
  final MainAxisAlignment horizontalAlignment;
  final CrossAxisAlignment verticalAlignment;
  final double borderCircularRadius;

  const LabeledCheckbox({
    super.key,
    required this.value,
    this.customLabel,
    this.margin = EdgeInsets.zero,
    this.textLabel = '',
    this.tristate = false,
    this.disabled = false,
    this.color = AppTheme.orangeColor,
    this.onChanged,
    this.validator,
    this.autovalidateMode = AutovalidateMode.disabled,
    this.errorPadding = EdgeInsets.zero,
    this.horizontalAlignment = MainAxisAlignment.start,
    this.borderCircularRadius = 2,
  })  : isSmallCheckbox = false,
        fillColor = null,
        verticalAlignment = CrossAxisAlignment.center;

  const LabeledCheckbox.small({
    super.key,
    required this.value,
    this.customLabel,
    this.margin = EdgeInsets.zero,
    this.textLabel = '',
    this.tristate = false,
    this.disabled = false,
    this.color = AppTheme.whiteColor,
    this.onChanged,
    this.validator,
    this.autovalidateMode = AutovalidateMode.disabled,
    this.fillColor = AppTheme.blueColor6,
    this.errorPadding = EdgeInsets.zero,
    this.horizontalAlignment = MainAxisAlignment.start,
    this.verticalAlignment = CrossAxisAlignment.start,
    this.borderCircularRadius = 4,
  }) : isSmallCheckbox = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: margin,
      child: FormField<bool>(
        autovalidateMode: autovalidateMode,
        validator: validator,
        builder: (fieldState) => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: horizontalAlignment,
              crossAxisAlignment: verticalAlignment,
              children: [
                // Checkbox pequeno
                if (isSmallCheckbox)
                  _Checkbox(
                    checkColor: color,
                    fieldState: fieldState,
                    fillColor: fillColor != null ? MaterialStateProperty.all(fillColor!) : null,
                    disabled: disabled,
                    onChanged: disabled ? null : onChanged,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderCircularRadius)),
                    tristate: tristate,
                    value: value,
                  ),

                // Checkbox padrão
                if (!isSmallCheckbox)
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: disabled ? Colors.grey : color,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(borderCircularRadius),
                    ),
                    margin: const EdgeInsets.only(right: 12),
                    child: SizedBox(
                      width: 18,
                      height: 18,
                      child: Theme(
                        data: ThemeData(unselectedWidgetColor: Colors.transparent),
                        child: _Checkbox(
                          disabled: disabled,
                          checkColor: color,
                          fieldState: fieldState,
                          onChanged: disabled ? null : onChanged,
                          tristate: tristate,
                          value: value,
                          side: BorderSide.none,
                        ),
                      ),
                    ),
                  ),
                if (textLabel.isNotEmpty || customLabel != null)
                  Flexible(
                    child: textLabel.isNotEmpty ? Text(textLabel, style: AppTheme.regular12White) : customLabel!,
                  ),
              ],
            ),
            if (fieldState.hasError) ...[
              const SizedBox(height: 4),
              Padding(
                padding: errorPadding,
                child: Text(
                  fieldState.errorText!,
                  style: AppTheme.regular12Black.copyWith(color: AppTheme.errorColor),
                ),
              )
            ]
          ],
        ),
      ),
    );
  }
}

class _Checkbox extends StatelessWidget {
  final Color? checkColor;
  final FormFieldState<bool>? fieldState;
  final MaterialStateProperty<Color>? fillColor;
  final Function(bool)? onChanged;
  final OutlinedBorder? shape;
  final BorderSide? side;
  final bool tristate;
  final bool? value;
  final bool disabled;

  const _Checkbox({
    this.checkColor,
    this.fieldState,
    this.fillColor,
    this.onChanged,
    this.shape,
    this.side,
    this.tristate = false,
    this.value,
    this.disabled = false,
  });

  @override
  Widget build(BuildContext context) {
    // TODO Trocar corda borda com base em disabled
    return Checkbox(
      activeColor: Colors.transparent,
      checkColor: disabled ? Colors.grey : checkColor,
      fillColor: fillColor,
      materialTapTargetSize: MaterialTapTargetSize.padded,
      onChanged: onChanged != null
          ? (value) async {
              await onChanged!(value ?? false);
              return fieldState?.didChange(value ?? false);
            }
          : null,
      shape: shape,
      side: side,
      tristate: tristate,
      visualDensity: VisualDensity.compact,
      value: value,
    );
  }
}
