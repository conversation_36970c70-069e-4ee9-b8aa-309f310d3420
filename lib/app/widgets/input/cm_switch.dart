import 'package:flutter/cupertino.dart';

import '../../config/app_theme.dart';
import '../loading/loading.dart';

class CMSwitch extends StatelessWidget {
  final String? text;
  final TextStyle? textStyle;
  final EdgeInsets textPadding;
  final bool isOn;
  final void Function(bool isOn)? onChanged;
  final Color trackColor;
  final Color activeColor;
  final Widget? trailing;
  final EdgeInsets padding;
  final TextDirection textDirection;
  final bool expandText;
  final bool isLoading;

  const CMSwitch({
    super.key,
    this.text,
    this.textStyle,
    this.textPadding = const EdgeInsets.only(top: 8, left: 5),
    required this.isOn,
    required this.onChanged,
    this.trackColor = AppTheme.greyColor3,
    this.activeColor = AppTheme.orangeColor,
    this.trailing,
    this.padding = EdgeInsets.zero,
    this.textDirection = TextDirection.ltr,
    this.expandText = false,
    this.isLoading = false,
  });

  Widget get _textWidget {
    final child = Padding(
      padding: textPadding,
      child: Text(text!, style: textStyle ?? AppTheme.regular14White),
    );
    if (expandText) return Expanded(child: child);
    return Flexible(child: child);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        textDirection: textDirection,
        children: [
          // Switch
          CupertinoSwitch(
            value: isOn,
            onChanged: isLoading ? null : onChanged,
            trackColor: trackColor,
            activeColor: activeColor,
          ),

          if (isLoading)
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 10),
              child: Loading(),
            ),

          // Texto
          if ((text ?? '').isNotEmpty) _textWidget,

          if (trailing != null) trailing!,
        ],
      ),
    );
  }
}
