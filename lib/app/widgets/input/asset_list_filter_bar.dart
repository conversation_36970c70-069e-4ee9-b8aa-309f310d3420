import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../list/applied_filters_list.dart';
import 'input.dart';

class AssetListFilterBar extends StatelessWidget {
  final String? initialValue;
  final String? hintText;
  final int? resultCount;
  final ValueChanged<String>? onTextChanged;
  final VoidCallback onVisualizationModeButtonPressed;
  final VoidCallback onFilterButtonPressed;
  final List<AppliedFilter> currentFilters;
  final EdgeInsets padding;

  const AssetListFilterBar({
    super.key,
    this.initialValue,
    this.hintText = 'Digite o produto',
    this.resultCount,
    this.onTextChanged,
    required this.onVisualizationModeButtonPressed,
    required this.onFilterButtonPressed,
    this.currentFilters = const [],
    this.padding = const EdgeInsets.symmetric(horizontal: 20),
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Input(
                  initialValue: initialValue,
                  hintText: hintText,
                  onChanged: onTextChanged,
                  padding: EdgeInsets.zero,
                  prefixIcon: const Icon(
                    Icons.search,
                    color: AppTheme.orangeColor,
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(
                  Icons.view_list_outlined,
                  color: AppTheme.orangeColor,
                  size: 35,
                ),
                onPressed: onVisualizationModeButtonPressed,
              ),
              IconButton(
                icon: const Icon(
                  Icons.sort_rounded,
                  color: AppTheme.orangeColor,
                  size: 35,
                ),
                onPressed: onFilterButtonPressed,
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: AppliedFiltersList(
                  filters: currentFilters,
                  padding: EdgeInsets.zero,
                ),
              ),
              if (resultCount != null)
                Text(
                  resultCount == 1 ? 'Exibindo 1 item' : 'Exibindo $resultCount itens',
                  style: AppTheme.regular10White,
                ),
            ],
          ),
        ],
      ),
    );
  }
}
