import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../config/constants.dart';
import '../../config/app_theme.dart';
import '../../utils/ui_utils.dart';
import '../info/app_tooltip.dart';

const InputBorder underlineBorderDefault = UnderlineInputBorder(borderSide: BorderSide(color: AppTheme.whiteColor));
const InputBorder underlineBorderLight = UnderlineInputBorder(borderSide: BorderSide(color: AppTheme.blueColorCardBg2));
const InputBorder underlineBorderError = UnderlineInputBorder(borderSide: BorderSide(color: AppTheme.errorColor));

final outlineBorderDefault = OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: const BorderSide(color: AppTheme.whiteColor));
final outlineBorderError = outlineBorderDefault.copyWith(borderSide: const BorderSide(color: AppTheme.errorColor));

class Input extends StatefulWidget {
  final String? hintText;
  final String? label;
  final void Function(String text)? onChanged;
  final void Function(String text)? onFieldSubmitted;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final FocusNode? focusNode;
  final TextStyle? labelStyle;
  final TextStyle? textStyle;
  final TextStyle? hintStyle;
  final bool obscureText;
  final String obscuringCharacter;
  final String? Function(String? text)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final VoidCallback? onTap;
  final bool enabled;
  final AutovalidateMode autovalidate;
  final Widget? prefixIcon;
  final dynamic prefixIconPath;
  final Widget? suffixIcon;
  final dynamic suffixIconPath;
  final double iconPathSize;
  final String? initialValue;
  final TextCapitalization textCapitalization;
  final bool readOnly;

  /// Força opacidade baixa a cima de outras regras.
  ///
  /// Valor default é `false`
  final bool forceLowerOpacity;

  /// Mantém opacidade normal (100%) independentemente de estado ativado/desativado.
  ///
  /// _Feito dessa forma para manter compatibilidade com implementações anteriores._
  ///
  /// Valor default é `true`
  final bool keepNormalOpacityRegardlessOfState;

  final bool useLightTextStyleWhenReadOnly;
  final int? maxLength;
  final int? maxLines;
  final InputType? type;
  final TextEditingController? controller;
  final bool autofocus;
  final bool darkMode;
  final EdgeInsets? padding;
  final Color? fillColor;
  final EdgeInsets contentPadding;
  final EdgeInsets scrollPadding;
  final double labelBottomPadding;
  final bool? showCursor;
  final InputBorder? customBorder;
  final BoxConstraints? inputConstraints;
  final bool showTooltipInfo;
  final String messageTooltipInfo;

  const Input({
    super.key,
    this.label,
    this.type,
    this.hintText,
    this.onChanged,
    this.onFieldSubmitted,
    this.keyboardType,
    this.textInputAction,
    this.focusNode,
    this.labelStyle,
    this.textStyle,
    this.hintStyle,
    this.validator,
    this.inputFormatters,
    this.onTap,
    this.prefixIcon,
    this.prefixIconPath,
    this.suffixIcon,
    this.suffixIconPath,
    this.iconPathSize = 16,
    this.obscureText = false,
    this.obscuringCharacter = '•',
    this.enabled = true,
    this.maxLength,
    this.maxLines = 1,
    this.autovalidate = AutovalidateMode.disabled,
    this.initialValue,
    this.readOnly = false,
    this.forceLowerOpacity = false,
    this.keepNormalOpacityRegardlessOfState = true,
    this.useLightTextStyleWhenReadOnly = true,
    this.textCapitalization = TextCapitalization.none,
    this.controller,
    this.autofocus = false,
    this.darkMode = true,
    this.padding,
    this.contentPadding = const EdgeInsets.only(right: 16),
    this.scrollPadding = const EdgeInsets.all(20),
    this.labelBottomPadding = 0,
    this.showCursor,
    this.customBorder,
    this.inputConstraints,
    this.showTooltipInfo = false,
    this.messageTooltipInfo = '',
  })  : fillColor = null,
        assert((prefixIconPath == null || prefixIconPath is IconData || prefixIconPath is String));

  /// Campo de texto com fundo colorido (branco por padrão).
  const Input.filled({
    super.key,
    this.label,
    this.type,
    this.hintText,
    this.onChanged,
    this.onFieldSubmitted,
    this.keyboardType,
    this.textInputAction,
    this.focusNode,
    this.labelStyle,
    this.textStyle,
    this.hintStyle,
    this.validator,
    this.inputFormatters,
    this.onTap,
    this.prefixIcon,
    this.prefixIconPath,
    this.suffixIcon,
    this.suffixIconPath,
    this.iconPathSize = 20,
    this.obscureText = false,
    this.obscuringCharacter = '•',
    this.enabled = true,
    this.maxLength,
    this.maxLines = 1,
    this.autovalidate = AutovalidateMode.disabled,
    this.initialValue,
    this.readOnly = false,
    this.forceLowerOpacity = false,
    this.keepNormalOpacityRegardlessOfState = true,
    this.textCapitalization = TextCapitalization.none,
    this.controller,
    this.autofocus = false,
    this.darkMode = true,
    this.padding,
    this.fillColor = AppTheme.whiteColor,
    this.contentPadding = const EdgeInsets.symmetric(horizontal: 16),
    this.scrollPadding = const EdgeInsets.all(20),
    this.labelBottomPadding = 5,
    this.showCursor,
    this.customBorder,
    this.inputConstraints,
    this.showTooltipInfo = false,
    this.messageTooltipInfo = '',
  })  : useLightTextStyleWhenReadOnly = false,
        assert((prefixIconPath == null || prefixIconPath is IconData || prefixIconPath is String));

  static final inputLabelStyle = AppTheme.defaultText.merge(const TextStyle(color: AppTheme.whiteColor, fontSize: 12, fontWeight: FontWeight.bold));

  static final inputHintStyle = AppTheme.defaultText.merge(const TextStyle(color: Colors.white54));

  static final inputLabelLightStyle = AppTheme.defaultText.merge(const TextStyle(color: AppTheme.blackColor, fontSize: 12, fontWeight: FontWeight.bold));

  static final inputHintLightStyle = AppTheme.defaultText.merge(const TextStyle(color: Colors.black45));

  @override
  State<Input> createState() => InputState();
}

class InputState extends State<Input> {
  // Define se já houve alguma validação no input. Por padrão, é false.
  bool _isValidated = false;
  // Define se o input é válido ou não após a última validação realizada.
  bool? _isValid;
  String? _errorText;
  late FocusNode focusNode;
  late TextEditingController _textEditingController;
  bool _obscureText = true;

  bool get _isFilled => widget.fillColor != null;

  @override
  void initState() {
    super.initState();
    _textEditingController = widget.controller ?? TextEditingController(text: widget.initialValue);

    focusNode = widget.focusNode ?? FocusNode();
  }

  /// Insere uma mensagem de erro programaticamente no input.
  ///
  /// Exemplo de uso num StatelessWidget:
  /// ```dart
  /// final GlobalKey<InputState> _currentKey = GlobalKey();
  ///
  /// Widget get input => Input(key: _currentKey, ...);
  ///
  /// void programmaticallyAddError() => _currentKey.currentState.updateError("Erro adicionado neste input.");
  /// ```
  void updateError([String? error]) {
    setState(() => _errorText = error);
    setState(() => _isValidated = true);
    if (error != null) {
      setState(() => _isValid = false);
    } else {
      setState(() => _isValid = true);
    }
  }

  void resetValidation() {
    setState(() => _errorText = null);
    setState(() => _isValid = null);
    setState(() => _isValidated = false);
  }

  TextStyle get _textStyle {
    var style = _isFilled ? AppTheme.medium14Grey : AppTheme.defaultText.copyWith(color: widget.darkMode ? AppTheme.whiteColor : AppTheme.blackColor);
    if (widget.readOnly && widget.useLightTextStyleWhenReadOnly) {
      style = (widget.darkMode ? Input.inputHintStyle : Input.inputHintLightStyle);
    }

    return style.merge(widget.textStyle);
  }

  TextStyle get _hintStyle {
    var hintStyle = _isFilled
        ? AppTheme.medium14Grey2
        : widget.darkMode
            ? Input.inputHintStyle
            : Input.inputHintLightStyle;

    return hintStyle.merge(widget.hintStyle);
  }

  Widget? _icon(CMIconAffix affix) {
    Widget? icon = (affix == CMIconAffix.prefix) ? widget.prefixIcon : widget.suffixIcon;
    dynamic iconPath = (affix == CMIconAffix.prefix) ? widget.prefixIconPath : widget.suffixIconPath;

    if (iconPath != null) {
      return iconify(
        iconPath,
        color: _isValid ?? true
            ? _isFilled
                ? AppTheme.greyColor
                : AppTheme.orangeColor
            : AppTheme.errorColor,
        size: widget.iconPathSize,
      );
    }

    if (affix == CMIconAffix.suffix && widget.type == InputType.secret) {
      return GestureDetector(
        onTap: () => setState(() => _obscureText = !_obscureText),
        child: Padding(
          padding: EdgeInsets.only(right: _isFilled ? 10 : 0),
          child: Icon(
            _obscureText ? Icons.visibility_outlined : Icons.visibility_off_outlined,
            color: _isFilled ? AppTheme.greyColor2 : Colors.white54,
          ),
        ),
      );
    }

    // No caso do sufixo não ter sido definido, é definido automaticamente com base no estado do input.
    if (affix == CMIconAffix.suffix && widget.suffixIcon == null && _isValidated) {
      Widget validationIcon;
      if (_isValid ?? true) {
        validationIcon = const Icon(Icons.check, color: AppTheme.orangeColor);
      } else {
        validationIcon = InkWell(
          customBorder: const CircleBorder(),
          onTap: () => setState(
            () {
              _textEditingController.clear();
              _isValidated = false;
              _isValid = null;
            },
          ),
          child: const Icon(
            Icons.clear_rounded,
            color: AppTheme.errorColor,
          ),
        );
      }
      return Padding(
        padding: _isFilled ? const EdgeInsets.only(right: 8) : EdgeInsets.zero,
        child: validationIcon,
      );
    }

    return icon;
  }

  Widget _label() => widget.label != null
      ? Padding(
          padding: EdgeInsets.only(bottom: widget.labelBottomPadding),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  widget.label!,
                  style: widget.labelStyle ?? (widget.darkMode ? Input.inputLabelStyle : Input.inputLabelLightStyle),
                ),
              ),
              if (widget.showTooltipInfo)
                AppTooltip(
                  message: widget.messageTooltipInfo,
                  child: const Center(
                    child: Icon(
                      Icons.info_outline,
                      color: AppTheme.whiteColor,
                    ),
                  ),
                )
            ],
          ),
        )
      : const SizedBox.shrink();

  Widget _input() {
    return TextFormField(
      controller: _textEditingController,
      onFieldSubmitted: widget.onFieldSubmitted,
      onChanged: widget.onChanged,
      autovalidateMode: widget.autovalidate,
      autofocus: widget.autofocus,
      focusNode: focusNode,
      obscureText: widget.obscureText && _obscureText,
      obscuringCharacter: widget.obscuringCharacter,
      textInputAction: widget.textInputAction,
      keyboardType: widget.keyboardType,
      onTap: widget.onTap,
      validator: (value) {
        setState(() => _isValidated = true);
        if (widget.validator == null) return null;

        final error = widget.validator!(value);
        setState(() => _isValid = error == null);

        if (error != null) FocusScope.of(context).autofocus(focusNode);

        return error;
      },
      inputFormatters: widget.inputFormatters,
      enabled: widget.enabled,
      textCapitalization: widget.textCapitalization,
      readOnly: widget.readOnly,
      maxLines: widget.maxLines,
      maxLength: widget.maxLength ?? widget.type?.textMaxLength,
      maxLengthEnforcement: MaxLengthEnforcement.enforced,
      cursorColor: widget.darkMode && !_isFilled ? AppTheme.whiteColor : AppTheme.blueColorCardBg2,
      style: _textStyle,
      scrollPadding: widget.scrollPadding,
      showCursor: widget.showCursor,
      decoration: (_isFilled
              ? InputDecoration(
                  filled: true,
                  border: widget.customBorder ?? outlineBorderDefault,
                  enabledBorder: widget.customBorder ?? outlineBorderDefault,
                  disabledBorder: widget.customBorder ?? outlineBorderDefault,
                  focusedBorder: widget.customBorder ??
                      (widget.readOnly ? outlineBorderDefault : outlineBorderDefault.copyWith(borderSide: const BorderSide(color: AppTheme.orangeColor, width: 2))),
                  focusedErrorBorder: widget.customBorder ?? outlineBorderDefault.copyWith(borderSide: const BorderSide(color: AppTheme.errorColor, width: 2)),
                  errorBorder: widget.customBorder ?? outlineBorderError,
                  contentPadding: widget.contentPadding,
                  constraints: widget.inputConstraints,
                )
              : widget.darkMode
                  ? InputDecoration(
                      filled: false,
                      border: widget.customBorder ?? underlineBorderDefault,
                      enabledBorder: widget.customBorder ?? underlineBorderDefault,
                      disabledBorder: widget.customBorder ?? underlineBorderDefault,
                      focusedBorder: widget.customBorder ?? underlineBorderDefault.copyWith(borderSide: const BorderSide(color: AppTheme.orangeColor, width: 2)),
                      focusedErrorBorder: widget.customBorder ?? underlineBorderError.copyWith(borderSide: const BorderSide(color: AppTheme.errorColor, width: 2)),
                      errorBorder: widget.customBorder ?? underlineBorderError,
                      helperStyle: const TextStyle(color: AppTheme.whiteColor),
                      contentPadding: widget.contentPadding,
                      constraints: widget.inputConstraints,
                    )
                  : InputDecoration(
                      filled: false,
                      border: widget.customBorder ?? underlineBorderLight,
                      enabledBorder: widget.customBorder ?? underlineBorderLight,
                      disabledBorder: widget.customBorder ?? underlineBorderLight,
                      focusedBorder: widget.customBorder ?? underlineBorderLight.copyWith(borderSide: const BorderSide(color: AppTheme.orangeColor, width: 2)),
                      focusedErrorBorder: widget.customBorder ?? underlineBorderError.copyWith(borderSide: const BorderSide(color: AppTheme.errorColor, width: 2)),
                      errorBorder: widget.customBorder ?? underlineBorderError,
                      helperStyle: const TextStyle(color: AppTheme.blueColorCardBg2),
                      contentPadding: widget.contentPadding,
                      constraints: widget.inputConstraints,
                    ))
          .copyWith(
        // Fundo
        filled: _isFilled,
        fillColor: widget.fillColor,
        // Ícones
        prefixIcon: _icon(CMIconAffix.prefix),
        prefixIconConstraints: const BoxConstraints(minWidth: 32, maxWidth: 32),
        suffixIcon: _icon(CMIconAffix.suffix),
        suffixIconConstraints: const BoxConstraints(minWidth: 32, maxWidth: 32),
        // Outros
        errorText: _errorText,
        hintText: widget.hintText,
        hintStyle: _hintStyle,
        helperStyle: TextStyle(color: widget.darkMode ? AppTheme.whiteColor : AppTheme.blueColorCardBg2),
        errorStyle: const TextStyle(color: AppTheme.errorColor),
        errorMaxLines: 3,
        counterText: "",
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    double opacity = Constants.disabledOpacity;

    if (widget.enabled || widget.keepNormalOpacityRegardlessOfState) {
      opacity = 1;
    }
    if (widget.forceLowerOpacity) {
      opacity = Constants.disabledOpacity;
    }

    return Opacity(
      opacity: opacity,
      child: Container(
        constraints: const BoxConstraints(minHeight: 48.0),
        padding: widget.padding ?? const EdgeInsets.only(bottom: 25),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _label(),
            _input(),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    if (widget.focusNode == null) focusNode.dispose();
    if (widget.controller == null) _textEditingController.dispose();
    super.dispose();
  }
}

enum InputType {
  bankAccountNumberDigit(1),
  bankBranchCode(4),

  /// Senha/Assinatura
  secret(6),
  bankAccountNumber(12);

  final int textMaxLength;

  const InputType(this.textMaxLength);
}

enum CMIconAffix { prefix, suffix }
