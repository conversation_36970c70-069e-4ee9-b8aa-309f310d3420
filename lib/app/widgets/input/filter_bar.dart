import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../config/app_theme.dart';
import '../../models/sort_direction.dart';
import '../../utils/ui_utils.dart';
import '../info/badge.dart';
import 'input.dart';

class FilterBar extends StatefulWidget {
  final String? initialValue;
  final ValueChanged<String>? onTextFilter;
  final VoidCallback? onButtonFilter;
  final bool? displayCounter;
  final bool? displayButtonFilter;
  final int? filterCount;
  final BoxDecoration? decoration;
  final String hintText;

  /// Quando não é nulo, ícone `Icons.swap_vert` para mostrar lista de opções de ordenação é mostrado.
  final List<FilterSortOption>? sortOptions;
  final Function(FilterSortOption)? onSort;
  final String? currentSort;

  /// Quando não é nulo e [sortOptions] é nulo, ícone `Icons.sort_rounded` para abrir tela de ordenação é mostrado.
  final Function()? onTapSort;

  const FilterBar({
    super.key,
    this.initialValue,
    this.onTextFilter,
    this.onButtonFilter,
    this.displayCounter,
    this.displayButtonFilter,
    this.decoration,
    this.filterCount,
    this.sortOptions,
    this.onSort,
    this.currentSort,
    this.onTapSort,
    this.hintText = 'Digite o produto',
  });

  @override
  State<StatefulWidget> createState() => _FilterBarState();
}

class _FilterBarState extends State<FilterBar> {
  bool shouldShowOptions = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      decoration: widget.decoration ?? const BoxDecoration(color: AppTheme.blueColorCardBg),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Input(
                  initialValue: widget.initialValue,
                  hintText: widget.hintText,
                  padding: EdgeInsets.zero,
                  prefixIcon: const Icon(
                    Icons.search,
                    color: AppTheme.orangeColor,
                  ),
                  onChanged: widget.onTextFilter,
                ),
              ),
              const SizedBox(width: 14),

              // Botão para tela de ordenação
              if (widget.sortOptions == null && widget.onTapSort != null && widget.displayButtonFilter != false)
                IconButton(
                  padding: const EdgeInsets.only(right: 14),
                  icon: const Icon(
                    Icons.sort_rounded,
                    color: AppTheme.orangeColor,
                    size: 35,
                  ),
                  onPressed: widget.onTapSort,
                ),

              // Botão para tela de filtro
              if (widget.displayButtonFilter != false)
                if (widget.sortOptions == null)
                  IntrinsicHeight(
                    child: InkWell(
                      onTap: widget.onButtonFilter,
                      child: SvgPicture.asset('assets/icons/action_filter_orange.svg'),
                    ),
                  )

                // Botão para lista de opções de ordenação
                else
                  InkWell(
                    onTap: () => setState(() => shouldShowOptions = !shouldShowOptions),
                    child: iconify(Icons.swap_vert, size: 30),
                  ),
            ],
          ),
          if (widget.currentSort != null || widget.displayCounter == true)
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(top: 20),
              child: Wrap(
                alignment: WrapAlignment.spaceBetween,
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  if (widget.currentSort != null)
                    InkWell(
                      onTap: widget.onTapSort,
                      child: CMBadge(text: widget.currentSort ?? '', margin: EdgeInsets.zero, backgroundColor: AppTheme.blueColor),
                    )
                  else
                    const SizedBox.shrink(),
                  if (widget.displayCounter == true)
                    Text(
                      'Exibindo ${widget.filterCount} resultados',
                      style: AppTheme.semi12White,
                      textAlign: TextAlign.right,
                    ),
                ],
              ),
            ),
          AnimatedSize(
            curve: Curves.easeInOut,
            duration: const Duration(milliseconds: 400),
            child: Column(
              children: [
                const SizedBox(height: 0, width: double.infinity), // força a animação a ficar vertical
                if (shouldShowOptions && widget.sortOptions != null && widget.sortOptions!.isNotEmpty) ...[
                  const SizedBox(height: 15),
                  getSortOptionsWidget(),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget getSortOptionsWidget() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(5),
      child: SizedBox(
        height: 150,
        child: RawScrollbar(
          thumbVisibility: true,
          thumbColor: AppTheme.orangeColor,
          radius: const Radius.circular(3),
          thickness: 4,
          child: ListView.builder(
            itemCount: widget.sortOptions!.length,
            itemBuilder: (BuildContext context, int index) {
              return InkWell(
                onTap: () {
                  widget.onSort?.call(widget.sortOptions![index]);
                  setState(() => shouldShowOptions = false);
                },
                child: Container(
                  color: index % 2 == 0 ? AppTheme.blueColor : AppTheme.blueColor3,
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Text(widget.sortOptions![index].label, style: AppTheme.regular14White),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}

class FilterSortOption {
  String fieldName;
  String label;
  SortDirection direction;

  FilterSortOption({
    required this.fieldName,
    required this.label,
    required this.direction,
  });
}
