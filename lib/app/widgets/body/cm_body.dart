import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../buttons/button.dart';
import '../loading/loading.dart';

enum DataState { loading, empty, fulfilled, error }

/// Wrapper que define um conteúdo que toma todo o espaço disponível da tela, decorado com uma borda customizada no canto superior esquerdo.
class CMBody<T> extends StatefulWidget {
  final Widget child;
  final LinearGradient? customGradient;

  /// Define se é exibido um `CMLoading`. Útil para quando se quer controlar o carregamento externamente.
  final bool isLoading;

  /// Define se o Widget se altera entre os estados de carregamento, vazio, obtido e com erro quando onDataChanged é chamado.
  final Future<T> Function()? onGetData;

  // Vazio
  final String emptyText;
  final String emptyButtonText;
  final Function()? onEmptyButtonPressed;
  final bool ignoreEmptyState;

  // Erro
  final String errorText;
  final String errorButtonText;
  final Function()? onErrorButtonPressed;

  // Carregando
  final String loadingText;
  final Widget? loadingWidget;

  const CMBody({
    super.key,
    required this.child,
    this.onGetData,
    this.customGradient,
    this.emptyText = 'Não há itens para exibir.',
    this.emptyButtonText = 'Atualizar',
    this.onEmptyButtonPressed,
    this.ignoreEmptyState = false,
    this.errorText = 'Houve um erro ao carregar os itens dessa página.',
    this.errorButtonText = 'Tentar novamente',
    this.onErrorButtonPressed,
    this.isLoading = false,
    this.loadingText = 'Carregando itens...',
    this.loadingWidget,
  });

  /// Definido dessa forma pra que possa ser reutilizado de outras formas (e.g. outro container sem a borda customizada).
  static LinearGradient get defaultGradient {
    return const LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [AppTheme.blueColorCardBg, AppTheme.darkColor],
    );
  }

  @override
  CMBodyState createState() => CMBodyState();
}

class CMBodyState<T> extends State<CMBody> {
  late DataState _state;

  @override
  void initState() {
    super.initState();
    _state = widget.onGetData == null ? DataState.fulfilled : DataState.loading;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (widget.onGetData != null) {
      onChanged();
    }
  }

  Future<void> onChanged() async {
    try {
      setState(() => _state = DataState.loading);
      T data = await widget.onGetData?.call();

      if (!mounted) return;

      if ((data == null || (data is Iterable && data.isEmpty) || (data is String && data.isEmpty)) && !widget.ignoreEmptyState) {
        setState(() => _state = DataState.empty);
      } else {
        setState(() => _state = DataState.fulfilled);
      }
    } catch (error) {
      setState(() => _state = DataState.error);
    }
  }

  void setStateLoading() {
    setState(() => _state = DataState.loading);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 1000),
      curve: Curves.easeIn,
      decoration: BoxDecoration(
        gradient: widget.customGradient ?? CMBody.defaultGradient,
        borderRadius: const BorderRadius.only(topLeft: Radius.circular(30)),
      ),
      child: widget.onGetData != null
          ? (() {
              switch (_state) {
                case DataState.empty:
                  return DatalessStateWidget.empty(
                      text: widget.emptyText,
                      buttonText: widget.emptyButtonText,
                      onButtonPressed: () async {
                        await widget.onEmptyButtonPressed?.call();
                        onChanged();
                      });
                case DataState.fulfilled:
                  return SizedBox.expand(child: widget.child);
                case DataState.error:
                  return DatalessStateWidget.error(
                      text: widget.errorText,
                      buttonText: widget.errorButtonText,
                      onButtonPressed: () async {
                        await widget.onErrorButtonPressed?.call();
                        onChanged();
                      });
                case DataState.loading:
                default:
                  return widget.loadingWidget ?? _LoadingStateWidget(loadingText: widget.loadingText);
              }
            }())
          : widget.isLoading
              ? (widget.loadingWidget ?? _LoadingStateWidget(loadingText: widget.loadingText))
              : SizedBox.expand(child: widget.child),
    );
  }
}

class DatalessStateWidget extends StatelessWidget {
  final String text;
  final String? buttonText;
  final VoidCallback? onButtonPressed;

  const DatalessStateWidget({
    super.key,
    required this.text,
    this.buttonText,
    this.onButtonPressed,
  });

  /// Widget a exibir quando a página não retorna nenhum dado (e.g. API não retorna dados por não existirem).
  factory DatalessStateWidget.empty({required String text, String? buttonText, VoidCallback? onButtonPressed}) {
    return DatalessStateWidget(
      text: text,
      buttonText: buttonText,
      onButtonPressed: onButtonPressed,
    );
  }

  /// Widget a exibir quando a página não consegue obter os dados corretamente (e.g. API retorna erro ou null/string vazia).
  factory DatalessStateWidget.error({required String text, String? buttonText, VoidCallback? onButtonPressed}) {
    return DatalessStateWidget(
      text: text,
      buttonText: buttonText,
      onButtonPressed: onButtonPressed,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 80, vertical: 10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            text,
            textAlign: TextAlign.center,
            style: AppTheme.defaultText,
          ),
          if (buttonText != null && onButtonPressed != null) ...[
            const SizedBox(height: 24),
            Button.elevated(
              text: buttonText!,
              onPressed: onButtonPressed,
            ),
          ]
        ],
      ),
    );
  }
}

// Widget a ser exibido quando o conteúdo está sendo carregado.
class _LoadingStateWidget extends StatelessWidget {
  final String loadingText;

  const _LoadingStateWidget({required this.loadingText});

  @override
  Widget build(BuildContext context) {
    return Loading(description: loadingText);
  }
}
