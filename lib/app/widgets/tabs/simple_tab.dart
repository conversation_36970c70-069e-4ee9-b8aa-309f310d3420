import 'package:flutter/material.dart';

import '../../config/app_theme.dart';

@Deprecated('Use Tabs instead')
class SimpleTab extends StatefulWidget {
  final List<SimpleTabOption> options;
  final int selectedTab;
  final TextStyle? selectedTabStyle;
  final TextStyle? unselectedTabStyle;
  final EdgeInsets padding;

  /// [true] por padrão. Define se a tab selecionada é focada logo ao inicializar o Widget.
  /// Útil ao inicializar uma página numa tab específica, mas pode atrapalhar se a tab estiver no fim da página.
  final bool focusOnInit;

  const SimpleTab({
    super.key,
    required this.options,
    this.selectedTab = 0,
    this.selectedTabStyle,
    this.unselectedTabStyle,
    this.padding = const EdgeInsets.symmetric(horizontal: 20.0),
    this.focusOnInit = true,
  });

  @override
  State<SimpleTab> createState() => _SimpleTabState();
}

class _SimpleTabState extends State<SimpleTab> {
  final selectedKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    if (widget.focusOnInit == true) {
      Future.delayed(const Duration(milliseconds: 100)).then(
        (_) {
          if (selectedKey.currentContext == null) return;
          Scrollable.ensureVisible(
            selectedKey.currentContext!,
            duration: const Duration(milliseconds: 100),
          );
        },
      );
    }
  }

  List<Widget> get _tabs {
    return widget.options.asMap().entries.map(
      (entry) {
        final optionIndex = entry.key;
        final option = entry.value;

        return InkWell(
          key: widget.selectedTab == optionIndex ? selectedKey : null,
          onTap: () {
            if (option.enabled && option.onTap != null) {
              Future.delayed(const Duration(milliseconds: 100)).then(
                (_) {
                  if (selectedKey.currentContext == null) return;
                  Scrollable.ensureVisible(
                    selectedKey.currentContext!,
                    duration: const Duration(milliseconds: 100),
                  );
                },
              );
              option.onTap?.call();
            }
          },
          child: Container(
            decoration: widget.selectedTab == optionIndex
                ? const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: AppTheme.orangeColor,
                        width: 4,
                      ),
                    ),
                  )
                : null,
            margin: const EdgeInsets.fromLTRB(20, 20, 20, 0),
            padding: const EdgeInsets.only(bottom: 5),
            child: Text(
              option.label,
              maxLines: 2,
              overflow: TextOverflow.clip,
              style: option.enabled == false
                  ? AppTheme.regular16White.copyWith(color: AppTheme.transparentWhiteColor)
                  : optionIndex == widget.selectedTab
                      ? widget.selectedTabStyle ?? AppTheme.semi16Orange
                      : widget.unselectedTabStyle ?? AppTheme.regular16White,
            ),
          ),
        );
      },
    ).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 0.1),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned.fill(
            child: Padding(
              padding: widget.padding,
              child: Container(
                decoration: const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: AppTheme.blueColor,
                      width: 1,
                    ),
                  ),
                ),
              ),
            ),
          ),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            physics: const BouncingScrollPhysics(),
            child: Row(
              children: _tabs,
            ),
          ),
        ],
      ),
    );
  }
}

class SimpleTabOption {
  final GlobalKey? key;
  final String label;
  final void Function()? onTap;
  final bool enabled;

  SimpleTabOption({
    this.key,
    required this.label,
    this.onTap,
    this.enabled = true,
  });
}
