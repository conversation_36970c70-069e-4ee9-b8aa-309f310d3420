import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

import '../../config/app_theme.dart';

class SortOptions extends StatefulWidget {
  final List<CMSortOption> options;
  final int itemsPerLine;
  final Function(int, CMSortOption) onChange;

  const SortOptions({
    super.key,
    required this.options,
    required this.itemsPerLine,
    required this.onChange,
  });

  @override
  State<SortOptions> createState() => _SortOptionsState();
}

class _SortOptionsState extends State<SortOptions> {
  @override
  Widget build(BuildContext context) {
    return Wrap(
      runSpacing: 10,
      spacing: 8,
      alignment: WrapAlignment.start,
      children: widget.options
          .mapIndexed(
            (index, option) => InkWell(
              borderRadius: const BorderRadius.all(Radius.circular(5.0)),
              splashColor: AppTheme.blueColorCardBg2,
              onTap: () {
                final currentState = option.state;
                // limpa todas as opções
                for (final option in widget.options) {
                  option.state = SortState.none;
                }
                // avança o estado da opção selecionada
                switch (currentState) {
                  case SortState.none:
                    option.state = SortState.desc;
                    break;
                  case SortState.desc:
                    option.state = SortState.asc;
                    break;
                  case SortState.asc:
                  default:
                    option.state = SortState.none;
                }
                widget.onChange(index, option);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12.5, horizontal: 24),
                decoration: BoxDecoration(
                  color: option.state != SortState.none ? AppTheme.orangeColor : Colors.transparent,
                  borderRadius: const BorderRadius.all(Radius.circular(5.0)),
                  border: Border.all(color: option.state != SortState.none ? AppTheme.orangeColor : AppTheme.whiteColor),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      option.label,
                      style: AppTheme.semi14White,
                      textAlign: TextAlign.center,
                    ),
                    if (option.state == SortState.desc) const Icon(Icons.arrow_downward, color: AppTheme.whiteColor, size: 14),
                    if (option.state == SortState.asc) const Icon(Icons.arrow_upward, color: AppTheme.whiteColor, size: 14),
                  ],
                ),
              ),
            ),
          )
          .toList(),
    );
  }
}

enum SortState { none, asc, desc }

class CMSortOption {
  final String label;
  final dynamic value;
  SortState state;

  CMSortOption({
    required this.label,
    this.value,
    this.state = SortState.none,
  });
}
