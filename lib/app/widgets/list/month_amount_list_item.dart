import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../utils/extensions.dart';
import '../info/amount_colored_chip.dart';
import '../info/border_wrap.dart';
import '../info/vertical_field.dart';

class MonthAmountListItem extends StatelessWidget {
  const MonthAmountListItem({
    super.key,
    required this.onPressed,
    required this.month,
    required this.amountLabel,
    required this.amount,
    this.amountMask = AmountMask.currency,
    this.amountFontsize = 14,
  });

  final void Function() onPressed;
  final DateTime? month;
  final String amountLabel;
  final double amount;
  final AmountMask amountMask;
  final double amountFontsize;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: BorderWrap(
        children: [
          // Mês
          VerticalField(
            'Mês',
            month?.toMonthAndYear() ?? '',
            widthFactor: 0.4,
          ),

          // Valor
          VerticalField(
            amountLabel,
            AmountColoredChip(
              margin: const EdgeInsets.only(top: 2),
              amount,
              fontSize: amountFontsize,
              mask: amountMask,
              showPlusSign: true,
            ),
            widthFactor: 0.5,
          ),

          // Ícone de seta
          const SizedBox(
            height: 50,
            child: Icon(Icons.chevron_right, color: AppTheme.orangeColor),
          ),
        ],
      ),
    );
  }
}
