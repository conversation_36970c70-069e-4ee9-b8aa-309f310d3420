import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../utils/ui_utils.dart';

class BulletedList extends StatelessWidget {
  /// {@macro utils.iconify}
  final dynamic icon;
  final Color iconColor;
  final double iconSize;
  final List<String> items;

  /// Quando nulo, `AppTheme.bold14White` é utilizado.
  final TextStyle? itemStyle;

  const BulletedList({
    super.key,
    this.icon = 'assets/icons/arrow_right.svg',
    this.iconColor = AppTheme.whiteColor,
    this.iconSize = 11,
    required this.items,
    this.itemStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: List.generate(
        items.length,
        (index) => Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              iconify(icon, color: iconColor, size: iconSize),
              const SizedBox(width: 10),
              Expanded(
                child: Text(
                  items[index],
                  style: itemStyle ?? AppTheme.bold14White,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
