import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../config/app_theme.dart';
import '../../utils/extensions.dart';
import '../info/badge.dart';

/// Exibe uma lista de chips com os filtros aplicados na lista sendo exibida. O conteúdo exibido são transformados.
class AppliedFiltersList extends StatelessWidget {
  final List<AppliedFilter> filters;
  final EdgeInsets padding;

  const AppliedFiltersList({
    super.key,
    this.filters = const [],
    this.padding = const EdgeInsets.fromLTRB(20, 10, 20, 0),
  });

  /// Transforma o valor bruto do filtro em uma mensagem mais amigável ao humano.
  String getValue(AppliedFilter filter) {
    dynamic value;
    if (filter.value is List<double>) {
      dynamic init = filter.value.first.toInt();
      dynamic end = filter.value.last.toInt();
      if (filter.label == "Aplicação mínima") {
        init = (init as int).asCurrency();
        end = (end as int).asCurrency();
      }
      value = 'De $init a $end';
    }
    if (filter.value is Iterable<String>) {
      value = (filter.value as Iterable<String>).join(', ');
    }
    if (filter.value is bool) {
      value = filter.value == true ? 'Sim' : 'Não';
    }
    if (filter.label == 'Status') {
      value = filter.value != true ? 'Em andamento' : 'Encerrados';
    }
    if (filter.value is String) {
      value = filter.value;
    }
    if (filter.value is DateTime) {
      value = DateFormat('dd/MM/yyyy').format(filter.value);
    }
    return value;
  }

  bool isNotEmpty(AppliedFilter entry) {
    if (entry.value is bool) {
      return entry.value != null;
    }
    return entry.value != null && (entry.value is DateTime || entry.value.isNotEmpty);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      child: filters.isNotEmpty == true
          ? Wrap(
              spacing: 10,
              runSpacing: 10,
              children: filters.where(isNotEmpty).map(
                (filter) {
                  String value = getValue(filter);
                  return InkWell(
                    onTap: filter.onFilterTap,
                    child: CMBadge(
                      text: '${(filter.label ?? '').isNotEmpty ? '${filter.label}: ' : ''}$value',
                      margin: EdgeInsets.zero,
                      backgroundColor: AppTheme.blueColor,
                    ),
                  );
                },
              ).toList(),
            )
          : const SizedBox.shrink(),
    );
  }
}

class AppliedFilter {
  String? label;
  dynamic value;
  VoidCallback? onFilterTap;

  AppliedFilter({
    this.label,
    this.value,
    this.onFilterTap,
  });
}
