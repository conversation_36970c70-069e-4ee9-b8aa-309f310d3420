import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../info/amount_colored_chip.dart';
import 'app_scrollbar.dart';

class TradeFixedHeaderList extends StatefulWidget {
  const TradeFixedHeaderList({
    super.key,
    required this.fullWidthLabel,
    required this.fullWidthValue,
    required this.topLeftLabel,
    required this.topLeftValue,
    required this.topRightLabel,
    required this.topRightValue,
    this.bottomLeftLabel,
    this.bottomLeftValue,
    this.bottomRightLabel,
    this.bottomRightValue,
    required this.totalItems,
    required this.itemBuilder,
    this.listBoxDecoration,
    this.listSeparator = const SizedBox(height: 12),
    this.listBottomInformation,
  }) : assert(
          (bottomLeftLabel == null && bottomLeftValue == null && bottomRightLabel == null && bottomRightValue == null) ||
              (bottomLeftLabel != null && bottomLeftValue != null && bottomRightLabel != null && bottomRightValue != null),
        );

  final String fullWidthLabel;
  final String fullWidthValue;
  final String topLeftLabel;
  final String topLeftValue;
  final String topRightLabel;
  final AmountColoredChip topRightValue;
  final String? bottomLeftLabel;
  final String? bottomLeftValue;
  final String? bottomRightLabel;
  final String? bottomRightValue;
  final int totalItems;
  final Widget? Function(BuildContext, int) itemBuilder;
  final Widget listSeparator;
  final BoxDecoration? listBoxDecoration;
  final Widget? listBottomInformation;

  @override
  State<TradeFixedHeaderList> createState() => _TradeFixedHeaderListState();
}

class _TradeFixedHeaderListState extends State<TradeFixedHeaderList> {
  late final _scrollController = ScrollController();
  var _showSeparator = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    final shouldShowSeparator = _scrollController.offset > 0;
    if (shouldShowSeparator != _showSeparator) setState(() => _showSeparator = shouldShowSeparator);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Cabeçalho
        Padding(
          padding: AppTheme.pagePadding.copyWith(bottom: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(widget.fullWidthLabel, style: AppTheme.regular12Orange),
              Text(widget.fullWidthValue, style: AppTheme.semi14White),
              const SizedBox(height: 8),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(widget.topLeftLabel, style: AppTheme.regular12Orange),
                        Text(widget.topLeftValue, style: AppTheme.semi14White),
                        if (widget.bottomLeftLabel != null) ...[
                          const SizedBox(height: 8),
                          Text(widget.bottomLeftLabel!, style: AppTheme.regular12Orange),
                          Text(widget.bottomLeftValue!, style: AppTheme.semi14White),
                        ],
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(widget.topRightLabel, style: AppTheme.regular12Orange),
                      const SizedBox(height: 2),
                      widget.topRightValue,
                      if (widget.bottomRightLabel != null) ...[
                        const SizedBox(height: 8),
                        Text(widget.bottomRightLabel!, style: AppTheme.regular12Orange),
                        Text(widget.bottomRightValue!, style: AppTheme.semi14White),
                      ],
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),

        // Separador
        AnimatedOpacity(
          opacity: _showSeparator ? 1 : 0,
          duration: const Duration(milliseconds: 200),
          child: const Divider(height: 1, color: Color(0xFF053D6A)),
        ),

        // Lista de operações
        Expanded(
          child: AppScrollbar(
            controller: _scrollController,
            child: SingleChildScrollView(
              controller: _scrollController,
              padding: AppTheme.pagePadding.copyWith(top: 0),
              child: Column(
                children: [
                  // Lista
                  Container(
                    decoration: widget.listBoxDecoration,
                    child: ListView.separated(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      separatorBuilder: (_, __) => widget.listSeparator,
                      itemCount: widget.totalItems,
                      itemBuilder: widget.itemBuilder,
                    ),
                  ),

                  // Informação final
                  if (widget.listBottomInformation != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 24),
                      child: widget.listBottomInformation!,
                    ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
