import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../api/investments/investments_api.dart';
import '../../api/investments/oferta-publica/oferta_publica_cronograma.dart';
import '../../config/app_theme.dart';
import '../../utils/extensions.dart';
import '../body/CM_body.dart';
import '../info/app_tooltip.dart';

class PublicOfferScheduleList extends StatelessWidget {
  final List<OfertaPublicaCronograma>? cronograma;
  final OfertaPublica? oferta;

  const PublicOfferScheduleList({
    super.key,
    required this.cronograma,
    required this.oferta,
  });

  @override
  Widget build(BuildContext context) {
    if ((cronograma ?? []).isEmpty) return DatalessStateWidget.empty(text: 'Não há cronograma para exibir.');
    return SingleChildScrollView(
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 40.0, horizontal: 20.0),
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.white,
          ),
          borderRadius: BorderRadius.circular(5.0),
        ),
        child: ListView.separated(
          shrinkWrap: true,
          itemCount: cronograma!.length,
          separatorBuilder: (context, index) => const Divider(
            height: 1,
            color: Colors.white24,
          ),
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) => Row(
            children: [
              Expanded(
                flex: 3,
                child: Container(
                  alignment: Alignment.centerLeft,
                  constraints: const BoxConstraints(minHeight: 60),
                  decoration: const BoxDecoration(border: Border(right: BorderSide(color: Colors.white24))),
                  padding: const EdgeInsets.fromLTRB(10, 10, 35, 10),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (oferta?.ipoOferPubDtBookbuild != null &&
                          oferta!.ipoOferPubDtBookbuild!.isNotEmpty &&
                          DateFormat('dd/MM/yyyy').parse(oferta!.ipoOferPubDtBookbuild!) == cronograma![index].data)
                        const AppTooltip(
                          message:
                              //'É o processo utilizado para definir um preço justo para o IPO ou oferta secundária de ações, que seja adequado à intenção de compra dos investidores.',
                              'Data em que o preço do ativo será conhecido.',
                          child: Center(
                            child: Icon(
                              Icons.info_rounded,
                              color: AppTheme.whiteColor,
                            ),
                          ),
                        )
                      else if (oferta?.ipoOferPubDtLiquid != null &&
                          oferta!.ipoOferPubDtLiquid!.isNotEmpty &&
                          DateFormat('dd/MM/yyyy').parse(oferta!.ipoOferPubDtLiquid!) == cronograma![index].data)
                        const AppTooltip(
                          message: 'Data em que as ações/cotas serão liquidadas.',
                          child: Center(
                            child: Icon(
                              Icons.info_rounded,
                              color: AppTheme.whiteColor,
                            ),
                          ),
                        ),
                      const SizedBox(width: 10),
                      Flexible(
                        child: Text(
                          cronograma![index].nome ?? '',
                          style: AppTheme.regular12White,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                alignment: Alignment.center,
                constraints: const BoxConstraints(minHeight: 60),
                padding: const EdgeInsets.fromLTRB(10, 10, 10, 10),
                child: Text(
                  cronograma![index].data.toddMMyyyy() ?? '',
                  style: AppTheme.regular12Orange,
                  softWrap: false,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
