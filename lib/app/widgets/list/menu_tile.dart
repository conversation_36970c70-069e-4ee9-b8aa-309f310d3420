import 'package:flutter/material.dart';

import '../../config/app_theme.dart';

class MenuTile extends StatelessWidget {
  final String title;
  final VoidCallback onTap;
  final IconData? icon;
  final Widget? badge;

  const MenuTile({
    super.key,
    required this.title,
    required this.onTap,
    this.icon,
    this.badge,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          if (icon != null)
            Padding(
              padding: const EdgeInsets.only(right: 15),
              child: Icon(
                icon!,
                size: 20,
                color: AppTheme.whiteColor,
              ),
            ),
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 15),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: AppTheme.transparentWhiteColor,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: AppTheme.regular16White,
                  ),
                  if (badge != null) badge!,
                  const Spacer(),
                  const Icon(
                    Icons.arrow_forward_ios,
                    size: 15,
                    color: AppTheme.whiteColor,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
