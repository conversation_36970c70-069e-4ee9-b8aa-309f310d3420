import 'package:flutter/material.dart';

import '../../config/app_theme.dart';

class AppScrollbar extends StatelessWidget {
  final Widget child;
  final ScrollController? controller;
  final bool thumbVisibility;
  final Color? thumbColor;
  final Radius? radius;
  final double? thickness;
  final EdgeInsets? padding;
  final bool trackVisibility;
  final Color? trackColor;
  final Radius? trackRadius;
  final bool? interactive;

  const AppScrollbar({
    super.key,
    required this.child,
    this.controller,
    this.thumbVisibility = false,
    this.thumbColor = AppTheme.orangeColor,
    this.radius = const Radius.circular(8),
    this.thickness = 8,
    this.padding = const EdgeInsets.all(4),
    this.trackVisibility = false,
    this.trackColor,
    this.trackRadius,
    this.interactive = true,
  });

  @override
  Widget build(BuildContext context) {
    return RawScrollbar(
      controller: controller,
      thumbVisibility: thumbVisibility,
      thumbColor: thumbColor,
      radius: radius,
      thickness: thickness,
      // Adiciona padding inferior para respeitar limites da tela
      padding: padding?.copyWith(bottom: (padding?.bottom ?? 0) + MediaQuery.of(context).viewPadding.bottom),
      trackVisibility: trackVisibility,
      trackColor: trackColor,
      trackRadius: trackRadius,
      interactive: interactive,
      child: child,
    );
  }
}
