import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../body/cm_body.dart';
import '../info/trade_history_header.dart';
import '../loading/pull_to_refresh.dart';

class TradeHiredHistoryPageList extends StatelessWidget {
  const TradeHiredHistoryPageList({
    super.key,
    required this.onRefresh,
    required this.header,
    required this.totalItems,
    required this.emptyText,
    required this.itemBuilder,
  });

  final Future<void> Function() onRefresh;
  final TradeHistoryHeader header;
  final int totalItems;
  final String emptyText;
  final Widget Function(BuildContext context, int index) itemBuilder;

  @override
  Widget build(BuildContext context) {
    return PullToRefresh(
      onRefresh: onRefresh,
      child: totalItems > 0
          ? SingleChildScrollView(
              child: Padding(
                padding: AppTheme.pagePadding,
                child: Column(
                  children: [
                    // Cabeçalho
                    header,

                    // Lista
                    const SizedBox(height: 16),
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      separatorBuilder: (_, __) => const SizedBox(height: 16),
                      itemCount: totalItems,
                      itemBuilder: itemBuilder,
                    ),

                    // Informação
                    const SizedBox(height: 24),
                    const Text(
                      'As recomendações serão registradas como encerradas apenas quando encerradas pela equipe da CM Capital conforme recomendação/estratégia. Caso o cliente cancele o serviço ou realize o encerramento das operações manualmente, as posições e preços de execução apresentadas no histórico poderão não refletir as informações reais do cliente.',
                      style: AppTheme.regular12White,
                    ),
                  ],
                ),
              ),
            )
          : SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: SizedBox(
                height: Get.height - 460,
                child: DatalessStateWidget.empty(
                  text: emptyText,
                ),
              ),
            ),
    );
  }
}
