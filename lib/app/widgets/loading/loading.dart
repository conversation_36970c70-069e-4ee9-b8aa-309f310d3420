import 'package:flutter/material.dart';

import '../../config/app_theme.dart';

class Loading extends StatelessWidget {
  final String? description;
  final Color color;
  final Color backgroundColor;

  const Loading({
    super.key,
    this.description,
    this.color = AppTheme.orangeColor,
    this.backgroundColor = AppTheme.blueColor,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircularProgressIndicator(
          backgroundColor: backgroundColor,
          valueColor: AlwaysStoppedAnimation(color),
        ),
        if (description != null) ...[
          const Divider(color: Colors.transparent),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 90),
            child: Text(
              description!,
              textAlign: TextAlign.center,
              style: AppTheme.defaultText,
            ),
          ),
        ]
      ],
    );
  }
}
