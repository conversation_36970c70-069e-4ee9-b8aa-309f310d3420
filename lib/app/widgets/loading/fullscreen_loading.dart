import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'loading.dart';

void showFullscreenLoading() {
  Get.dialog(
    const PopScope(
      canPop: false,
      child: FullscreenLoading(),
    ),
    barrierDismissible: false,
    useSafeArea: false,
  );
}

class FullscreenLoading extends StatelessWidget {
  const FullscreenLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
      child: const Center(
        child: Loading(),
      ),
    );
  }
}
